#!/usr/bin/env python3
"""
测试视频分类功能的脚本
使用方法：python test/test_video_categories.py
"""

import sys
import os
import asyncio
from dotenv import load_dotenv

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 加载环境变量
load_dotenv()

from src.services.video_category_service import get_channel_video_categories
from src.utils.logger import setup_logging_from_config, get_logger

# 初始化日志
setup_logging_from_config()
logger = get_logger('test_video_categories')

def test_video_categories():
    """测试视频分类功能"""
    
    # 测试频道ID（可以替换为任何有效的YouTube频道ID）
    test_channel_id = "UCXuqSBlHAE6Xw-yeJA0Tunw"  # Linus Tech Tips
    
    try:
        logger.info("开始测试视频分类功能...")
        
        # 测试获取频道视频分类
        result = get_channel_video_categories(
            channel_id=test_channel_id,
            max_results=5,
            region_code='US'
        )
        
        print("\n" + "="*50)
        print("视频分类测试结果")
        print("="*50)
        print(f"频道ID: {result['channel_id']}")
        print(f"视频总数: {result['total_count']}")
        print("\n视频分类详情:")
        print("-"*50)
        
        for video in result['videos']:
            print(f"视频ID: {video['video_id']}")
            print(f"分类ID: {video['category_id']}")
            print(f"分类名称: {video['category_title']}")
            print("-"*30)
        
        logger.info("视频分类功能测试成功!")
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        print(f"\n测试失败: {str(e)}")
        return False

def test_individual_functions():
    """测试各个独立功能"""
    from src.services.video_category_service import (
        get_channel_video_ids,
        get_video_categories,
        get_category_titles
    )
    
    test_channel_id = "UCXuqSBlHAE6Xw-yeJA0Tunw"
    
    try:
        print("\n" + "="*50)
        print("测试独立功能")
        print("="*50)
        
        # 1. 测试获取视频ID
        print("1. 获取频道视频ID...")
        video_ids = get_channel_video_ids(test_channel_id, 3)
        print(f"获取到的视频ID: {video_ids}")
        
        if not video_ids:
            print("没有获取到视频ID，跳过后续测试")
            return False
        
        # 2. 测试获取视频分类
        print("\n2. 获取视频分类信息...")
        video_categories = get_video_categories(video_ids)
        print(f"视频分类信息: {video_categories}")
        
        # 3. 测试获取分类标题
        print("\n3. 获取分类标题...")
        category_ids = [vc['category_id'] for vc in video_categories]
        category_titles = get_category_titles(category_ids, 'US')
        print(f"分类标题: {category_titles}")
        
        logger.info("独立功能测试成功!")
        return True
        
    except Exception as e:
        logger.error(f"独立功能测试失败: {str(e)}")
        print(f"\n独立功能测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("YouTube视频分类功能测试")
    print("="*50)
    
    # 检查API密钥
    api_key = os.getenv("YOUTUBE_API_KEY")
    if not api_key:
        print("错误: 未配置YouTube API密钥")
        print("请在.env文件中设置YOUTUBE_API_KEY")
        return False
    
    print(f"使用API密钥: {api_key[:10]}...")
    
    # 运行测试
    success = True
    
    # 测试完整功能
    if not test_video_categories():
        success = False
    
    # 测试独立功能
    if not test_individual_functions():
        success = False
    
    if success:
        print("\n" + "="*50)
        print("所有测试通过! ✅")
        print("="*50)
    else:
        print("\n" + "="*50)
        print("部分测试失败! ❌")
        print("="*50)
    
    return success

if __name__ == "__main__":
    main()
