#!/usr/bin/env python3
"""
视频分类功能集成测试
测试API端点和服务函数的集成
"""

import sys
import os
import json
from fastapi.testclient import TestClient
from dotenv import load_dotenv

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 加载环境变量
load_dotenv()

def test_api_integration():
    """测试API集成"""
    try:
        # 导入FastAPI应用
        from src.app import app
        
        # 创建测试客户端
        client = TestClient(app)
        
        # 测试数据
        test_data = {
            "channel_id": "UCXuqSBlHAE6Xw-yeJA0Tunw",  # Linus Tech Tips
            "max_results": 3,
            "region_code": "US"
        }
        
        print("测试API端点...")
        print(f"请求数据: {json.dumps(test_data, indent=2)}")
        
        # 发送POST请求
        response = client.post("/api/video/categories", json=test_data)
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("API测试成功!")
            print(f"响应数据: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            # 验证响应结构
            assert "channel_id" in result
            assert "videos" in result
            assert "total_count" in result
            assert result["channel_id"] == test_data["channel_id"]
            assert isinstance(result["videos"], list)
            assert isinstance(result["total_count"], int)
            
            # 验证视频数据结构
            for video in result["videos"]:
                assert "video_id" in video
                assert "category_id" in video
                assert "category_title" in video
            
            print("✅ API集成测试通过!")
            return True
        else:
            print(f"❌ API测试失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API集成测试异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_service_integration():
    """测试服务函数集成"""
    try:
        from src.services.video_category_service import get_channel_video_categories
        
        print("\n测试服务函数...")
        
        result = get_channel_video_categories(
            channel_id="UCXuqSBlHAE6Xw-yeJA0Tunw",
            max_results=3,
            region_code="US"
        )
        
        print("服务函数测试成功!")
        print(f"结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        # 验证结果结构
        assert "channel_id" in result
        assert "videos" in result
        assert "total_count" in result
        assert isinstance(result["videos"], list)
        assert isinstance(result["total_count"], int)
        
        print("✅ 服务函数集成测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 服务函数集成测试异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_error_handling():
    """测试错误处理"""
    try:
        from src.services.video_category_service import get_channel_video_categories
        
        print("\n测试错误处理...")
        
        # 测试无效频道ID
        try:
            result = get_channel_video_categories(
                channel_id="invalid_channel_id",
                max_results=1,
                region_code="US"
            )
            print("❌ 应该抛出异常但没有")
            return False
        except Exception as e:
            print(f"✅ 正确处理无效频道ID错误: {str(e)}")
        
        # 测试空频道ID
        try:
            result = get_channel_video_categories(
                channel_id="",
                max_results=1,
                region_code="US"
            )
            print("❌ 应该抛出异常但没有")
            return False
        except Exception as e:
            print(f"✅ 正确处理空频道ID错误: {str(e)}")
        
        print("✅ 错误处理测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 错误处理测试异常: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("YouTube视频分类功能集成测试")
    print("="*50)
    
    # 检查API密钥
    api_key = os.getenv("YOUTUBE_API_KEY")
    if not api_key:
        print("❌ 未配置YouTube API密钥")
        print("请在.env文件中设置YOUTUBE_API_KEY")
        return False
    
    print(f"使用API密钥: {api_key[:10]}...")
    
    # 运行测试
    tests = [
        ("API集成测试", test_api_integration),
        ("服务函数集成测试", test_service_integration),
        ("错误处理测试", test_error_handling),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name} 失败")
    
    print(f"\n{'='*50}")
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有集成测试通过!")
        return True
    else:
        print("❌ 部分测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
