#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YouTube数据采集服务命令行工具
用于采集YouTube频道和视频数据
"""

import os
import sys
import argparse
import logging
from dotenv import load_dotenv

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# 导入自定义模块
from src.utils.logger import setup_logging_from_config
from src.database.db_pool import DatabasePool
from src.spider.spider_youtube import get_channel_info, process_channel
from src.dao.channel_dao import ChannelDao
from src.dao.youtube_dao import YouTubeDao

# 设置日志格式
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def init():
    """初始化环境和数据库"""
    # 加载环境变量
    load_dotenv()

    # 初始化日志
    setup_logging_from_config()

    # 初始化数据库
    try:
        db_pool = DatabasePool()
        db_pool.init_clients()
        logger.info("数据库初始化成功")
    except Exception as e:
        logger.error(f"数据库初始化失败: {str(e)}")
        sys.exit(1)

    logger.info("环境初始化完成")

def fetch_channel(channel_id):
    """获取并存储YouTube频道信息"""
    try:
        if not channel_id:
            logger.error("频道ID不能为空")
            return False
        
        logger.info(f"开始获取YouTube频道信息: {channel_id}")
        result = get_channel_info(channel_id)
        
        if result:
            logger.info(f"成功采集频道 '{result.get('title')}' 的基本信息")
            return True
        else:
            logger.error(f"采集频道 {channel_id} 信息失败")
            return False
    except Exception as e:
        logger.error(f"获取YouTube频道信息时发生错误: {str(e)}")
        return False

def fetch_videos(channel_id, max_videos=None):
    """获取并存储YouTube频道的视频列表"""
    try:
        if not channel_id:
            logger.error("频道ID不能为空")
            return False
        
        logger.info(f"开始获取YouTube频道 {channel_id} 的视频列表")
        
        # 检查频道是否已存在
        channel_dao = ChannelDao()
        channel = channel_dao.get_channel_by_id(channel_id)
        
        if not channel:
            logger.info(f"频道 {channel_id} 不存在，先获取频道信息")
            if not fetch_channel(channel_id):
                logger.error(f"无法获取频道 {channel_id} 信息，视频抓取终止")
                return False
        
        # 导入需要的模块
        from src.spider.spider_youtube import YoutubeSpider
        
        # 从环境变量获取API密钥
        api_key = os.getenv("YOUTUBE_API_KEY", "")
        
        # 初始化爬虫
        spider = YoutubeSpider(api_key)
        
        # 处理频道视频
        try:
            process_channel(spider, channel_id)
            logger.info(f"成功处理频道 {channel_id} 的视频")
            return True
        except Exception as e:
            logger.error(f"处理频道 {channel_id} 视频失败: {str(e)}")
            return False
            
    except Exception as e:
        logger.error(f"获取YouTube视频信息时发生错误: {str(e)}")
        return False

def list_channels():
    """列出数据库中的所有YouTube频道"""
    try:
        channel_dao = ChannelDao()
        channels = channel_dao.get_all_channels()
        
        if not channels:
            logger.info("数据库中没有YouTube频道记录")
            return
        
        print("\n已存储的YouTube频道列表:")
        print("=" * 80)
        print(f"{'频道ID':<26} {'标题':<40} {'订阅数':<10} {'视频数':<8}")
        print("-" * 80)
        
        for channel in channels:
            print(f"{channel['youtube_id']:<26} {channel['title'][:38]:<40} {channel['subscriber_count']:<10} {channel['video_count']:<8}")
        
        print("=" * 80)
        print(f"共 {len(channels)} 个频道\n")
    except Exception as e:
        logger.error(f"列出频道时发生错误: {str(e)}")

def list_videos(channel_id=None, limit=10):
    """列出数据库中的视频"""
    try:
        youtube_dao = YouTubeDao()
        
        if channel_id:
            videos = youtube_dao.get_videos_by_channel_id(channel_id, limit=limit)
            channel_dao = ChannelDao()
            channel = channel_dao.get_channel_by_id(channel_id)
            channel_name = channel['title'] if channel else channel_id
            title = f"频道 '{channel_name}' 的视频列表 (最多显示{limit}条)"
        else:
            videos = youtube_dao.get_all_videos(limit=limit)
            title = f"所有视频列表 (最多显示{limit}条)"
        
        if not videos:
            if channel_id:
                logger.info(f"未找到频道 {channel_id} 的视频记录")
            else:
                logger.info("数据库中没有视频记录")
            return
        
        print(f"\n{title}")
        print("=" * 100)
        print(f"{'视频ID':<15} {'标题':<50} {'播放数':<10} {'发布日期':<20}")
        print("-" * 100)
        
        for video in videos:
            title = video['title']
            if len(title) > 47:
                title = title[:47] + "..."
            published_at = video['published_at'].strftime('%Y-%m-%d %H:%M') if video['published_at'] else 'N/A'
            print(f"{video['youtube_id']:<15} {title:<50} {video['view_count']:<10} {published_at:<20}")
        
        print("=" * 100)
        print(f"共 {len(videos)} 个视频\n")
    except Exception as e:
        logger.error(f"列出视频时发生错误: {str(e)}")

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='YouTube数据采集服务命令行工具')
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 获取频道信息命令
    channel_parser = subparsers.add_parser('channel', help='获取YouTube频道信息')
    channel_parser.add_argument('channel_id', help='YouTube频道ID')
    
    # 获取视频信息命令
    videos_parser = subparsers.add_parser('videos', help='获取YouTube频道的视频列表')
    videos_parser.add_argument('channel_id', help='YouTube频道ID')
    videos_parser.add_argument('--max', type=int, default=50, help='最多获取的视频数量 (默认: 50)')
    
    # 列出已存储的频道命令
    list_channels_parser = subparsers.add_parser('list-channels', help='列出已存储的YouTube频道')
    
    # 列出已存储的视频命令
    list_videos_parser = subparsers.add_parser('list-videos', help='列出已存储的YouTube视频')
    list_videos_parser.add_argument('--channel', help='按频道ID筛选视频')
    list_videos_parser.add_argument('--limit', type=int, default=20, help='显示的视频数量限制 (默认: 20)')
    
    return parser.parse_args()

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()
    
    # 初始化环境
    init()
    
    # 执行相应的命令
    if args.command == 'channel':
        fetch_channel(args.channel_id)
    elif args.command == 'videos':
        fetch_videos(args.channel_id, args.max)
    elif args.command == 'list-channels':
        list_channels()
    elif args.command == 'list-videos':
        list_videos(args.channel, args.limit)
    else:
        logger.error("未指定有效的命令，请使用 -h 或 --help 查看帮助")

if __name__ == '__main__':
    main() 