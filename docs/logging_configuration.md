# 日志配置说明

## 概述

项目已经优化了日志配置，支持根据环境变量动态调整日志级别：

- **开发环境 (development)**: 显示 INFO、WARNING、ERROR、CRITICAL 级别的日志
- **生产环境 (production)**: 只显示 ERROR、CRITICAL 级别的日志

## 配置方法

### 1. 环境变量设置

在 `.env` 文件中设置 `ENVIRONMENT` 变量：

```bash
# 开发环境（默认）
ENVIRONMENT=development

# 生产环境
ENVIRONMENT=production
```

### 2. 日志级别说明

| 环境 | 控制台输出 | 文件输出 | 说明 |
|------|------------|----------|------|
| development | INFO+ | INFO+ | 显示详细的调试和运行信息 |
| production | ERROR+ | ERROR+ | 只显示错误和严重问题 |

### 3. 日志文件

日志文件保存在 `logs/` 目录下：

- `logs/spider.log` - 通用日志文件
- `logs/error.log` - 错误日志文件（始终记录ERROR+级别）
- `logs/youtube_spider.log` - YouTube爬虫专用日志
- `logs/[module_name].log` - 各模块专用日志文件

## 使用方法

### 在代码中使用日志

```python
from src.utils.logger import get_logger

# 获取日志记录器
logger = get_logger('your_module_name')

# 记录不同级别的日志
logger.debug("调试信息")      # 开发环境不显示
logger.info("一般信息")       # 开发环境显示，生产环境不显示
logger.warning("警告信息")    # 开发环境显示，生产环境不显示
logger.error("错误信息")      # 两种环境都显示
logger.critical("严重错误")   # 两种环境都显示
```

### 应用启动时的日志配置

日志配置会在应用启动时自动加载：

```python
from src.utils.logger import setup_logging_from_config

# 设置日志配置（通常在应用启动时调用）
setup_logging_from_config()
```

## 优势

1. **环境感知**: 根据部署环境自动调整日志级别
2. **性能优化**: 生产环境减少日志输出，提高性能
3. **问题排查**: 开发环境提供详细信息，便于调试
4. **统一管理**: 通过环境变量统一控制所有模块的日志级别

## 注意事项

1. 修改 `ENVIRONMENT` 环境变量后需要重启应用才能生效
2. ERROR级别的日志始终会被记录到 `logs/error.log` 文件中
3. 第三方库（如urllib3、requests等）的日志级别被设置为WARNING，减少噪音
4. 日志文件会自动轮转，避免文件过大

## 测试日志配置

可以通过以下方式测试日志配置是否正常工作：

```python
from src.utils.logger import setup_logging_from_config, get_logger

# 设置日志配置
setup_logging_from_config()

# 获取测试logger
logger = get_logger('test')

# 测试各种日志级别
logger.info("这是INFO级别日志")
logger.warning("这是WARNING级别日志")
logger.error("这是ERROR级别日志")
```

根据当前的 `ENVIRONMENT` 设置，你会看到不同的输出结果。
