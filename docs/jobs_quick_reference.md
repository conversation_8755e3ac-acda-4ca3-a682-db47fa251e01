# 定时任务快速参考

## 任务概览表

| 任务名称 | 执行时间 | 功能描述 | 数据来源 | 主要操作表 | 状态 |
|---------|---------|---------|---------|-----------|------|
| **task_scheduler** | 持续运行 | 用户任务调度器 | 用户API请求 | ytb_tasks | ✅ 运行中 |
| **updateChanne_job** | 每日 00:10 | 频道信息更新 | YouTube API | ytb_channels, ytb_channel_stats_snapshots | ✅ 运行中 |
| **updateVideos_job** | 每日 01:00 | 视频数据更新(API) | YouTube API | ytb_videos, ytb_video_stats_snapshots | ✅ 运行中 |
| **statistics_job** | 每日 02:00 | 统计数据计算 | 快照数据 | ytb_channel_time_statistics | ✅ 运行中 |
| **updateChannelAll_job** | 每日 03:00 | 全量数据更新(yt-dlp) | yt-dlp工具 | ytb_channels, ytb_videos | ✅ 运行中 |
| **cleanup_job** | 每小时 | 临时文件清理 | 本地文件系统 | 无 | ❌ 已禁用 |

## 数据流向

```mermaid
graph TD
    A[YouTube API/yt-dlp] --> B[updateChanne_job 00:10]
    B --> C[ytb_channels]
    B --> D[ytb_channel_stats_snapshots]
    
    A --> E[updateVideos_job 01:00]
    E --> F[ytb_videos]
    E --> G[ytb_video_stats_snapshots]
    
    D --> H[statistics_job 02:00]
    H --> I[ytb_channel_time_statistics]
    
    A --> J[updateChannelAll_job 03:00]
    J --> C
    J --> F
    
    K[用户API请求] --> L[task_scheduler]
    L --> M[ytb_tasks]
    L --> F
    L --> G
```

## 快速故障排查

### 检查任务状态
```sql
-- 查看最近的任务执行情况
SELECT task_type, status, created_at, updated_at, error_message 
FROM ytb_tasks 
ORDER BY created_at DESC 
LIMIT 10;

-- 查看运行中的任务
SELECT * FROM ytb_tasks WHERE status = 'running';

-- 查看失败的任务
SELECT * FROM ytb_tasks WHERE status = 'failed' ORDER BY updated_at DESC LIMIT 5;
```

### 检查数据更新情况
```sql
-- 检查最新的频道快照
SELECT channel_id, snapshot_at, subscriber_count, view_count 
FROM ytb_channel_stats_snapshots 
ORDER BY snapshot_at DESC 
LIMIT 10;

-- 检查统计数据更新
SELECT channel_id, period_type, period_start, period_end, subscriber_change, view_change
FROM ytb_channel_time_statistics 
WHERE period_type = 'day' 
ORDER BY period_end DESC 
LIMIT 10;
```

### 常用日志命令
```bash
# 查看所有任务日志
tail -f logs/app.log | grep -E "(task_scheduler|statistics_job|updateChanne_job|updateVideos_job|updateChannelAll_job)"

# 查看错误日志
tail -f logs/error.log

# 查看特定任务的执行情况
grep "statistics_job" logs/app.log | tail -20

# 查看数据库连接问题
grep "connection pool" logs/app.log
```

## 紧急操作

### 重启所有任务
```bash
# 重启应用程序
sudo systemctl restart reyoutube-fastapi
# 或者
pkill -f "uvicorn src.app:app"
uv run -m uvicorn src.app:app --reload
```

### 手动执行任务
```python
# 手动执行统计任务
from src.utils.channel_stats_updater import ChannelStatsUpdater
from datetime import datetime, timedelta

target_date = datetime.now() - timedelta(days=1)
success = ChannelStatsUpdater.update_all_channel_statistics(target_date)

# 手动执行频道更新
from src.services.channel_service import update_channel_info_snapshot
success = update_channel_info_snapshot()
```

### 清理失败任务
```sql
-- 重置失败的任务为pending状态
UPDATE ytb_tasks 
SET status = 'pending', error_message = NULL, updated_at = NOW() 
WHERE status = 'failed' AND created_at > NOW() - INTERVAL '1 day';

-- 删除过期的成功任务记录
DELETE FROM ytb_tasks 
WHERE status = 'success' AND created_at < NOW() - INTERVAL '7 days';
```

## 性能监控

### 关键指标
- **任务成功率**: 每日成功任务数 / 总任务数
- **数据更新延迟**: 最新快照时间与当前时间的差值
- **API配额使用**: YouTube API调用次数统计
- **数据库性能**: 连接池使用率、查询响应时间

### 告警阈值建议
- 任务连续失败 > 3次
- 数据更新延迟 > 2小时
- 数据库连接池使用率 > 80%
- 磁盘使用率 > 90%

## 监控API使用

### 检查指定频道数据完整性
```bash
# 检查MrBeast频道最近7天数据完整性
curl -X GET "http://localhost:8100/monitoring/repair/channel/UCX6OQ3DkcsbYNE6H8uQQuVA/check?days=7"

# 检查T-Series频道最近5天数据完整性
curl -X GET "http://localhost:8100/monitoring/repair/channel/UCq-Fj5jknLsUf-MWSy4_brA/check?days=5"
```

### 修复指定频道数据
```bash
# 修复指定频道最近3天的统计数据（不修复快照数据）
curl -X POST "http://localhost:8100/monitoring/repair/channel/UCX6OQ3DkcsbYNE6H8uQQuVA?days=3&repair_snapshots=false&repair_statistics=true"

# 修复指定频道最近7天的所有数据
curl -X POST "http://localhost:8100/monitoring/repair/channel/UCq-Fj5jknLsUf-MWSy4_brA?days=7&repair_snapshots=true&repair_statistics=true"
```

### 手动触发任务
```bash
# 手动触发统计任务
curl -X POST "http://localhost:8100/monitoring/trigger/statistics?target_date=2025-07-21"

# 手动触发频道信息更新
curl -X POST "http://localhost:8100/monitoring/trigger/channel-update"

# 修复全局缺失数据
curl -X POST "http://localhost:8100/monitoring/repair/missing-data?days=3"
```

## 配置文件位置

| 配置项 | 文件路径 | 说明 |
|-------|---------|------|
| 环境变量 | `.env` | 数据库连接、API密钥等 |
| 日志配置 | `config/logging.yaml` | 日志级别、输出格式 |
| 任务启动 | `src/jobs/__init__.py` | 任务注册和启动 |
| 数据库初始化 | `docs/init.sql` | 数据库表结构 |

## 联系信息

如遇到紧急问题，请：
1. 查看详细文档：`docs/scheduled_jobs_documentation.md`
2. 检查日志文件：`logs/app.log` 和 `logs/error.log`
3. 查看任务状态：通过数据库查询或API接口
4. 必要时重启服务：按照上述重启步骤操作
