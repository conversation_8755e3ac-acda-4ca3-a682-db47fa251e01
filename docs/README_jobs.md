# 定时任务系统文档索引

## 文档概览

本目录包含了YouTube数据采集系统中所有定时任务的详细文档，帮助开发者和运维人员理解、维护和扩展定时任务系统。

## 文档结构

### 📋 主要文档

1. **[scheduled_jobs_documentation.md](./scheduled_jobs_documentation.md)**
   - **内容**: 定时任务系统的完整详细文档
   - **适用人群**: 开发者、系统架构师
   - **包含内容**: 
     - 每个任务的详细逻辑流程
     - 数据库表结构说明
     - API调用流程
     - 错误处理机制
     - 性能优化建议

2. **[jobs_quick_reference.md](./jobs_quick_reference.md)**
   - **内容**: 定时任务快速参考指南
   - **适用人群**: 运维人员、技术支持
   - **包含内容**:
     - 任务概览表格
     - 快速故障排查步骤
     - 常用SQL查询
     - 紧急操作指南

3. **[jobs_monitoring_api.md](./jobs_monitoring_api.md)**
   - **内容**: 任务监控API设计建议
   - **适用人群**: 开发者、DevOps工程师
   - **包含内容**:
     - 健康检查API
     - 任务统计API
     - 管理操作API
     - 监控集成建议

### 📊 任务系统架构

```
定时任务系统
├── 任务调度器 (task_scheduler.py)     - 用户任务调度
├── 频道信息更新 (updateChanne_job.py)  - API方式更新频道信息
├── 视频数据更新 (updateVideos_job.py)  - API方式更新视频数据
├── 统计数据计算 (statistics_job.py)   - 基于快照计算统计
├── 全量数据更新 (updateChannelAll_job.py) - yt-dlp方式全量更新
└── 文件清理任务 (cleanup_job.py)      - 临时文件清理(已禁用)
```

#### 手动记录：
1. updateChanne_job更新频道的每日数据之后， statistics_job才能统计数据到时间表ytb_channel_time_statistics，前端用于展示。 

### ⏰ 执行时间表

| 时间 | 任务 | 描述 |
|------|------|------|
| 00:10 | updateChanne_job | 频道信息和快照更新 |
| 01:00 | updateVideos_job | 视频数据更新(API方式) |
| 02:00 | statistics_job | 统计数据计算 |
| 03:00 | updateChannelAll_job | 全量数据更新(yt-dlp方式) |
| 持续 | task_scheduler | 用户任务调度 |

## 快速开始

### 1. 查看任务状态
```sql
-- 查看最近任务执行情况
SELECT task_type, status, created_at, error_message 
FROM ytb_tasks 
ORDER BY created_at DESC 
LIMIT 10;
```

### 2. 检查日志
```bash
# 查看所有任务日志
tail -f logs/app.log | grep -E "(task_scheduler|statistics_job|updateChanne_job)"

# 查看错误日志
tail -f logs/error.log
```

### 3. 重启任务系统
```bash
# 重启应用程序
sudo systemctl restart reyoutube-fastapi
```

## 常见问题

### Q: 任务长时间处于running状态怎么办？
**A**: 检查任务超时设置(默认5分钟)，查看详细日志确定卡住的步骤，必要时手动重置任务状态。

### Q: 数据库连接池耗尽怎么处理？
**A**: 检查数据库连接配置，增加连接池大小，或重启应用程序。

### Q: YouTube API配额超限怎么办？
**A**: 检查API密钥配额使用情况，等待配额重置，或考虑使用多个API密钥轮换。

### Q: 如何手动触发统计任务？
**A**: 可以通过Python代码手动执行，或者实现监控API后通过API触发。

## 监控建议

### 关键指标
- 任务执行成功率
- 数据更新时效性  
- API调用成功率
- 数据库性能指标

### 告警设置
- 连续任务失败 > 3次
- 数据更新延迟 > 2小时
- 数据库连接池使用率 > 80%
- 磁盘使用率 > 90%

## 开发指南

### 添加新任务
1. 在`src/jobs/`目录创建新任务文件
2. 实现异步任务函数和启动函数
3. 在`__init__.py`中注册新任务
4. 添加错误处理和日志记录
5. 更新相关文档

### 修改执行时间
```python
# 在任务文件中修改目标时间
target_time = now.replace(hour=4, minute=30, second=0, microsecond=0)
```

### 添加监控API
参考`jobs_monitoring_api.md`中的建议实现监控端点。

## 相关资源

### 代码文件
- `src/jobs/` - 定时任务实现
- `src/utils/channel_stats_updater.py` - 统计计算工具
- `src/services/` - 业务逻辑服务
- `src/dao/` - 数据访问层

### 配置文件
- `.env` - 环境变量配置
- `config/logging.yaml` - 日志配置
- `docs/init.sql` - 数据库表结构

### 日志文件
- `logs/app.log` - 应用日志
- `logs/error.log` - 错误日志
- `logs/spider.log` - 爬虫日志

## 联系方式

如有问题或建议，请：
1. 查看相关文档
2. 检查日志文件
3. 查询数据库状态
4. 联系开发团队

---

**最后更新**: 2024年1月
**文档版本**: v1.0
**维护者**: 开发团队
