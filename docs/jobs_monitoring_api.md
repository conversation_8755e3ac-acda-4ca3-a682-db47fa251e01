# 定时任务监控API建议

## 概述

为了更好地监控和管理定时任务系统，建议添加以下API端点来提供任务状态查询、健康检查和管理功能。

## 建议的API端点

### 1. 任务健康检查

```python
@router.get("/health/jobs", tags=["监控"])
async def jobs_health_check():
    """获取所有定时任务的健康状态"""
    try:
        # 检查各个任务的状态
        health_status = {
            "timestamp": datetime.now().isoformat(),
            "overall_status": "healthy",
            "tasks": {
                "task_scheduler": {
                    "status": "running",
                    "last_activity": get_last_task_activity(),
                    "pending_tasks": TaskDao.count_pending_tasks()
                },
                "statistics_job": {
                    "status": "scheduled",
                    "last_execution": get_last_statistics_execution(),
                    "next_execution": get_next_execution_time(2, 0)  # 凌晨2点
                },
                "update_channel_job": {
                    "status": "scheduled", 
                    "last_execution": get_last_channel_update(),
                    "next_execution": get_next_execution_time(0, 10)  # 凌晨0:10
                },
                "update_videos_job": {
                    "status": "scheduled",
                    "last_execution": get_last_videos_update(), 
                    "next_execution": get_next_execution_time(1, 0)   # 凌晨1:00
                },
                "update_channel_all_job": {
                    "status": "scheduled",
                    "last_execution": get_last_channel_all_update(),
                    "next_execution": get_next_execution_time(3, 0)   # 凌晨3:00
                }
            },
            "database": {
                "connection_pool_status": get_db_pool_status(),
                "active_connections": get_active_connections_count()
            }
        }
        
        return health_status
        
    except Exception as e:
        return {
            "timestamp": datetime.now().isoformat(),
            "overall_status": "unhealthy",
            "error": str(e)
        }
```

### 2. 任务执行统计

```python
@router.get("/jobs/statistics", tags=["监控"])
async def get_jobs_statistics(days: int = 7):
    """获取最近N天的任务执行统计"""
    try:
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        # 查询任务统计
        stats = TaskDao.get_task_statistics(start_date, end_date)
        
        return {
            "period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
                "days": days
            },
            "summary": {
                "total_tasks": stats.get("total", 0),
                "successful_tasks": stats.get("success", 0),
                "failed_tasks": stats.get("failed", 0),
                "success_rate": round(stats.get("success", 0) / max(stats.get("total", 1), 1) * 100, 2)
            },
            "by_task_type": stats.get("by_type", {}),
            "daily_breakdown": stats.get("daily", [])
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取任务统计失败: {str(e)}")
```

### 3. 数据更新状态

```python
@router.get("/jobs/data-status", tags=["监控"])
async def get_data_update_status():
    """获取数据更新状态"""
    try:
        # 检查最新的数据更新时间
        latest_channel_snapshot = ChannelDao.get_latest_snapshot_time()
        latest_video_update = YouTubeDao.get_latest_video_update_time()
        latest_statistics = ChannelStatsUpdater.get_latest_statistics_time()
        
        now = datetime.now()
        
        return {
            "timestamp": now.isoformat(),
            "data_freshness": {
                "channel_snapshots": {
                    "latest_update": latest_channel_snapshot.isoformat() if latest_channel_snapshot else None,
                    "hours_ago": (now - latest_channel_snapshot).total_seconds() / 3600 if latest_channel_snapshot else None,
                    "status": "fresh" if latest_channel_snapshot and (now - latest_channel_snapshot).total_seconds() < 86400 else "stale"
                },
                "video_data": {
                    "latest_update": latest_video_update.isoformat() if latest_video_update else None,
                    "hours_ago": (now - latest_video_update).total_seconds() / 3600 if latest_video_update else None,
                    "status": "fresh" if latest_video_update and (now - latest_video_update).total_seconds() < 86400 else "stale"
                },
                "statistics": {
                    "latest_update": latest_statistics.isoformat() if latest_statistics else None,
                    "hours_ago": (now - latest_statistics).total_seconds() / 3600 if latest_statistics else None,
                    "status": "fresh" if latest_statistics and (now - latest_statistics).total_seconds() < 86400 else "stale"
                }
            },
            "data_counts": {
                "total_channels": ChannelDao.count_active_channels(),
                "total_videos": YouTubeDao.count_total_videos(),
                "snapshots_today": ChannelDao.count_snapshots_today(),
                "videos_updated_today": YouTubeDao.count_videos_updated_today()
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取数据状态失败: {str(e)}")
```

### 4. 任务管理

```python
@router.post("/jobs/tasks/{task_id}/retry", tags=["管理"])
async def retry_failed_task(task_id: int):
    """重试失败的任务"""
    try:
        task = TaskDao.get_task_by_id(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        if task['status'] != 'failed':
            raise HTTPException(status_code=400, detail="只能重试失败的任务")
        
        # 重置任务状态
        TaskDao.update_task_status(task_id, "pending", error_message=None)
        
        return {
            "message": f"任务 {task_id} 已重置为待处理状态",
            "task_id": task_id,
            "new_status": "pending"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"重试任务失败: {str(e)}")

@router.delete("/jobs/tasks/cleanup", tags=["管理"])
async def cleanup_old_tasks(days: int = 7):
    """清理旧的任务记录"""
    try:
        cutoff_date = datetime.now() - timedelta(days=days)
        deleted_count = TaskDao.delete_old_tasks(cutoff_date)
        
        return {
            "message": f"已清理 {deleted_count} 条超过 {days} 天的任务记录",
            "deleted_count": deleted_count,
            "cutoff_date": cutoff_date.isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"清理任务失败: {str(e)}")
```

### 5. 手动触发任务

```python
@router.post("/jobs/trigger/statistics", tags=["管理"])
async def trigger_statistics_job(target_date: str = None):
    """手动触发统计任务"""
    try:
        if target_date:
            target_date_obj = datetime.strptime(target_date, "%Y-%m-%d")
        else:
            target_date_obj = datetime.now() - timedelta(days=1)
        
        # 在后台执行统计任务
        success = ChannelStatsUpdater.update_all_channel_statistics(target_date_obj)
        
        return {
            "message": "统计任务已手动触发",
            "target_date": target_date_obj.strftime("%Y-%m-%d"),
            "success": success
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"触发统计任务失败: {str(e)}")

@router.post("/jobs/trigger/channel-update", tags=["管理"])
async def trigger_channel_update():
    """手动触发频道信息更新"""
    try:
        from src.services.channel_service import update_channel_info_snapshot
        success = update_channel_info_snapshot()
        
        return {
            "message": "频道更新任务已手动触发",
            "success": success
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"触发频道更新失败: {str(e)}")
```

## 辅助函数实现

```python
def get_next_execution_time(hour: int, minute: int) -> str:
    """计算下次执行时间"""
    now = datetime.now()
    next_time = now.replace(hour=hour, minute=minute, second=0, microsecond=0)
    
    if now.hour >= hour:
        next_time = next_time + timedelta(days=1)
    
    return next_time.isoformat()

def get_db_pool_status() -> dict:
    """获取数据库连接池状态"""
    try:
        from src.database import get_db_pool
        db_pool = get_db_pool()
        return {
            "status": "healthy",
            "max_connections": db_pool.maxconn if hasattr(db_pool, 'maxconn') else "unknown",
            "available_connections": "unknown"  # 需要根据实际连接池实现
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e)
        }
```

## 使用示例

### 健康检查
```bash
curl -X GET "http://localhost:8100/health/jobs"
```

### 获取任务统计
```bash
curl -X GET "http://localhost:8100/jobs/statistics?days=7"
```

### 重试失败任务
```bash
curl -X POST "http://localhost:8100/jobs/tasks/123/retry"
```

### 手动触发统计任务
```bash
curl -X POST "http://localhost:8100/jobs/trigger/statistics" \
     -H "Content-Type: application/json" \
     -d '{"target_date": "2024-01-15"}'
```

## 集成建议

1. **添加到现有路由**: 将这些端点添加到 `src/routers/` 目录下的新文件中
2. **权限控制**: 为管理类API添加适当的权限验证
3. **监控集成**: 可以与Prometheus、Grafana等监控系统集成
4. **告警配置**: 基于这些API的响应配置自动告警

## 6. 指定频道数据修复

### 检查指定频道数据完整性
```python
@router.get("/repair/channel/{channel_id}/check", tags=["监控"])
async def check_channel_data_integrity(channel_id: str, days: int = 7):
    """检查指定频道的数据完整性

    Args:
        channel_id: 频道ID
        days: 检查最近多少天的数据 (默认7天)

    Returns:
        - overall_integrity_score: 整体完整性得分 (0-100)
        - status: healthy/warning/critical
        - details: 详细的缺失数据信息
    """
```

### 修复指定频道数据
```python
@router.post("/repair/channel/{channel_id}", tags=["管理"])
async def repair_channel_data(
    channel_id: str,
    background_tasks: BackgroundTasks,
    days: int = 7,
    repair_snapshots: bool = True,
    repair_statistics: bool = True
):
    """修复指定频道的缺失数据

    Args:
        channel_id: 频道ID
        days: 检查最近多少天的数据 (默认7天)
        repair_snapshots: 是否修复快照数据 (默认True)
        repair_statistics: 是否修复统计数据 (默认True)

    Returns:
        - message: 任务提交确认信息
        - repair_config: 修复配置详情
        - status: submitted
    """
```

## 使用示例

### 检查指定频道数据完整性
```bash
# 检查MrBeast频道最近7天的数据完整性
curl -X GET "http://localhost:8100/monitoring/repair/channel/UCX6OQ3DkcsbYNE6H8uQQuVA/check?days=7"

# 响应示例
{
  "timestamp": "2025-07-22T23:10:35.429919",
  "channel_id": "UCX6OQ3DkcsbYNE6H8uQQuVA",
  "channel_name": "MrBeast",
  "check_period": "2025-07-15 to 2025-07-22",
  "overall_integrity_score": 42.86,
  "status": "critical",
  "details": {
    "snapshots": {
      "expected": 7,
      "actual": 3,
      "missing": 4,
      "score": 42.86,
      "missing_dates": ["2025-07-15", "2025-07-16", "2025-07-17", "2025-07-19"]
    },
    "statistics": {
      "expected": 7,
      "actual": 3,
      "missing": 4,
      "score": 42.86,
      "missing_dates": ["2025-07-17", "2025-07-18", "2025-07-19", "2025-07-20"]
    }
  }
}
```

### 修复指定频道数据
```bash
# 修复MrBeast频道最近3天的统计数据（不修复快照数据）
curl -X POST "http://localhost:8100/monitoring/repair/channel/UCX6OQ3DkcsbYNE6H8uQQuVA?days=3&repair_snapshots=false&repair_statistics=true"

# 响应示例
{
  "message": "频道 UCX6OQ3DkcsbYNE6H8uQQuVA 数据修复任务已提交",
  "channel_id": "UCX6OQ3DkcsbYNE6H8uQQuVA",
  "channel_name": "MrBeast",
  "repair_config": {
    "days": 3,
    "repair_snapshots": false,
    "repair_statistics": true
  },
  "status": "submitted",
  "timestamp": "2025-07-22T23:10:43.343700"
}
```

### 错误处理
```bash
# 检查不存在的频道
curl -X GET "http://localhost:8100/monitoring/repair/channel/INVALID_CHANNEL_ID/check"

# 响应示例
{
  "detail": "频道 INVALID_CHANNEL_ID 不存在"
}
```

## 新增功能特点

### ✅ 精确定位
- **频道级别修复**: 可以针对特定频道进行数据修复
- **灵活配置**: 支持选择修复快照数据或统计数据
- **时间范围控制**: 可以指定检查和修复的天数范围

### ✅ 详细反馈
- **完整性评分**: 提供0-100的数据完整性得分
- **状态分级**: healthy/warning/critical三级状态
- **缺失详情**: 精确显示缺失数据的具体日期

### ✅ 智能修复
- **容错处理**: 使用增强的统计计算逻辑进行修复
- **问题记录**: 详细记录修复过程和结果
- **后台执行**: 修复任务在后台异步执行，不阻塞API响应

### ✅ 安全可靠
- **频道验证**: 修复前验证频道是否存在
- **参数校验**: 完整的输入参数验证
- **错误处理**: 完善的异常处理和错误信息返回

这些新增的API端点为指定频道的数据修复提供了强大而灵活的工具，大大提升了定时任务系统的可观测性和可管理性。
