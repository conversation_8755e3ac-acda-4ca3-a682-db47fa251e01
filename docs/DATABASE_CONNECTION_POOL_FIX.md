# 数据库连接池耗尽问题修复方案

## 问题分析

根据报错信息，系统出现了数据库连接池耗尽的问题：
```
connection pool exhausted
获取数据库连接失败: connection pool exhausted
```

### 问题原因

1. **多个后台任务同时运行**：系统中有多个定时任务在同时运行，包括：
   - `task_scheduler.py` - 任务调度器（每60秒查询数据库）
   - `updateChannelAll_job.py` - 频道数据更新任务
   - `updateVideos_job.py` - 视频数据更新任务
   - `updateChanne_job.py` - 频道快照更新任务
   - `statistics_job.py` - 统计数据更新任务

2. **数据库连接池配置过小**：默认最大连接数只有10个
3. **缺少连接失败处理机制**：当连接池耗尽时，循环没有终止机制

## 修复方案

### 1. 增加数据库连接池大小

**文件**: `src/database/db_pool.py`
- 将默认最小连接数从 2 增加到 5
- 将默认最大连接数从 10 增加到 20
- 添加连接池状态监控功能

**文件**: `.env-example`
- 添加推荐的连接池配置：
  ```
  DB_MIN_CONNECTIONS=5
  DB_MAX_CONNECTIONS=20
  ```

### 2. 添加数据库连接失败检测和处理机制

为所有后台任务添加了数据库连接失败的检测和处理：

#### 任务调度器 (`src/jobs/task_scheduler.py`)
- 添加数据库错误计数器
- 当连续失败5次时，停止任务调度器并记录关键日志
- 在数据库连接失败时抛出异常，让上层处理

#### 定时任务文件修改
以下文件都添加了相同的错误处理机制：
- `src/jobs/updateChannelAll_job.py`
- `src/jobs/updateVideos_job.py` 
- `src/jobs/updateChanne_job.py`
- `src/jobs/statistics_job.py`

**错误处理逻辑**：
- 检测到 "connection pool exhausted" 或 "获取数据库连接失败" 错误时
- 增加错误计数器
- 连续失败3次后，停止该任务循环
- 记录关键错误日志，提示检查数据库配置

#### DAO层修改 (`src/dao/task_dao.py`)
- `get_running_task()` 和 `get_next_pending_task()` 方法
- 当检测到连接池耗尽错误时，抛出异常而不是返回None
- 让上层任务调度器能够正确处理连接失败

### 3. 增强错误日志记录

**文件**: `src/database/db_pool.py`
- 在连接获取失败时记录连接池状态信息
- 添加 `get_pool_status()` 方法用于监控连接池状态

## 使用建议

### 1. 环境变量配置
在 `.env` 文件中设置合适的连接池大小：
```bash
DB_MIN_CONNECTIONS=5
DB_MAX_CONNECTIONS=20
```

### 2. 监控日志
关注以下关键日志：
- `数据库连接池耗尽，错误次数: X/Y`
- `数据库连接池连续失败 X 次，XXX_job 停止运行`
- `请检查数据库连接配置和连接池设置，或重启应用程序`

### 3. 故障恢复
当出现连接池耗尽问题时：
1. 检查数据库服务器状态
2. 检查网络连接
3. 考虑增加连接池大小
4. 重启应用程序

## 预期效果

1. **提高系统稳定性**：连接池耗尽时不会无限循环消耗资源
2. **快速故障定位**：清晰的错误日志帮助快速定位问题
3. **自动故障恢复**：任务在连接恢复后会自动重置错误计数
4. **资源保护**：防止因数据库连接问题导致系统崩溃

## 注意事项

1. 连接池大小需要根据实际并发需求调整
2. 数据库服务器也需要有足够的连接限制配置
3. 建议定期监控数据库连接使用情况
4. 在高并发场景下可能需要进一步优化连接使用策略
