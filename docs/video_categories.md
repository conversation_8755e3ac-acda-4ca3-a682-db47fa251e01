# YouTube视频分类功能

本文档介绍如何使用YouTube视频分类功能来获取频道下视频的分类信息。

## 功能概述

视频分类功能可以：
1. 获取指定YouTube频道下的视频列表
2. 获取每个视频的分类ID
3. 将分类ID转换为可读的分类名称
4. 返回完整的视频分类信息

## API接口

### POST /api/video/categories

获取频道视频分类信息

**请求参数：**
```json
{
    "channel_id": "UCXuqSBlHAE6Xw-yeJA0Tunw",
    "max_results": 5,
    "region_code": "US"
}
```

**参数说明：**
- `channel_id` (必需): YouTube频道ID
- `max_results` (可选): 最大获取视频数量，默认5
- `region_code` (可选): 地区代码，默认US

**响应示例：**
```json
{
    "channel_id": "UCXuqSBlHAE6Xw-yeJA0Tunw",
    "total_count": 5,
    "videos": [
        {
            "video_id": "dQw4w9WgXcQ",
            "category_id": "28",
            "category_title": "Science & Technology"
        },
        {
            "video_id": "oHg5SJYRHA0",
            "category_id": "24",
            "category_title": "Entertainment"
        }
    ]
}
```

## 使用示例

### 1. 通过API调用

```bash
curl -X POST "http://localhost:8000/api/video/categories" \
     -H "Content-Type: application/json" \
     -d '{
         "channel_id": "UCXuqSBlHAE6Xw-yeJA0Tunw",
         "max_results": 5,
         "region_code": "US"
     }'
```

### 2. Python代码示例

```python
import requests

# API调用
response = requests.post(
    "http://localhost:8000/api/video/categories",
    json={
        "channel_id": "UCXuqSBlHAE6Xw-yeJA0Tunw",
        "max_results": 5,
        "region_code": "US"
    }
)

result = response.json()
print(f"频道ID: {result['channel_id']}")
print(f"视频总数: {result['total_count']}")

for video in result['videos']:
    print(f"视频ID: {video['video_id']}")
    print(f"分类: {video['category_title']}")
```

### 3. 直接使用服务函数

```python
from src.services.video_category_service import get_channel_video_categories

result = get_channel_video_categories(
    channel_id="UCXuqSBlHAE6Xw-yeJA0Tunw",
    max_results=5,
    region_code="US"
)

for video in result['videos']:
    print(f'Video ID: {video["video_id"]}, Category: {video["category_title"]}')
```

## 服务函数说明

### get_channel_video_categories()

主要功能函数，获取频道视频分类信息。

**参数：**
- `channel_id` (str): YouTube频道ID
- `max_results` (int): 最大获取视频数量，默认5
- `region_code` (str): 地区代码，默认'US'

**返回：**
- `Dict[str, Any]`: 包含频道ID、视频分类列表和总数的字典

### get_channel_video_ids()

获取频道下的视频ID列表。

**参数：**
- `channel_id` (str): YouTube频道ID
- `max_results` (int): 最大获取视频数量

**返回：**
- `List[str]`: 视频ID列表

### get_video_categories()

获取视频的分类ID。

**参数：**
- `video_ids` (List[str]): 视频ID列表

**返回：**
- `List[Dict[str, str]]`: 包含视频ID和分类ID的字典列表

### get_category_titles()

获取分类ID对应的分类名称。

**参数：**
- `category_ids` (List[str]): 分类ID列表
- `region_code` (str): 地区代码，默认'US'

**返回：**
- `Dict[str, str]`: 分类ID到分类名称的映射字典

## 常见分类ID和名称

| 分类ID | 分类名称 (US) |
|--------|---------------|
| 1      | Film & Animation |
| 2      | Autos & Vehicles |
| 10     | Music |
| 15     | Pets & Animals |
| 17     | Sports |
| 19     | Travel & Events |
| 20     | Gaming |
| 22     | People & Blogs |
| 23     | Comedy |
| 24     | Entertainment |
| 25     | News & Politics |
| 26     | Howto & Style |
| 27     | Education |
| 28     | Science & Technology |

## 测试和示例

### 运行测试

```bash
# 运行测试脚本
python test/test_video_categories.py

# 运行示例脚本
python examples/video_categories_example.py
```

### 测试不同地区

不同地区可能有不同的分类名称：

```python
regions = ['US', 'CN', 'JP', 'GB', 'DE']
for region in regions:
    result = get_channel_video_categories(
        channel_id="UCXuqSBlHAE6Xw-yeJA0Tunw",
        max_results=2,
        region_code=region
    )
    print(f"{region}: {[v['category_title'] for v in result['videos']]}")
```

## 错误处理

函数会抛出以下异常：
- `HTTPException`: YouTube API请求失败
- `ValueError`: 参数验证失败
- `Exception`: 其他未预期的错误

所有错误都会被记录到日志中，便于调试。

## 配置要求

确保在`.env`文件中配置了有效的YouTube API密钥：

```
YOUTUBE_API_KEY=your_youtube_api_key_here
```

## 注意事项

1. YouTube API有配额限制，请合理使用
2. 某些视频可能没有分类信息，会显示为"Unknown"
3. 不同地区的分类名称可能不同
4. 私有或受限制的视频可能无法获取分类信息
