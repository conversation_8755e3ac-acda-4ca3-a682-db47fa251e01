# 定时任务系统详细文档

## 概述

本系统包含6个主要的定时任务，负责YouTube频道和视频数据的自动化采集、处理和统计。所有任务都采用异步架构，支持错误处理和自动重试机制。

## 任务启动方式

所有定时任务通过 `src/jobs/__init__.py` 中的 `start_all()` 函数统一启动：

```python
# 启动命令
uv run -m uvicorn src.app:app --reload

# 任务启动顺序
async def start_all():
    await asyncio.gather(
        task_scheduler.start(),      # 任务调度器
        statistics_job.start(),      # 统计数据更新
        updateChanne_job.start(),    # 频道信息更新
        updateVideos_job.start(),    # 视频数据更新(API方式)
        updateChannelAll_job.start() # 频道和视频更新(yt-dlp方式)
    )
```

---

## 1. 任务调度器 (task_scheduler.py)

### 功能描述
后台任务调度器，负责检查和执行用户通过API提交的频道视频获取任务。

### 执行时间
- **检查间隔**: 每60秒检查一次待处理任务
- **处理间隔**: 任务完成后等待10秒再检查
- **超时时间**: 单个任务超时时间为5分钟

### 核心逻辑流程

#### 1.1 任务状态检查
```python
# 检查运行中的任务
running_task = TaskDao.get_running_task()

# 检查任务超时
if time_diff > task_timeout:
    TaskDao.update_task_status(task_id, "failed", error_message="任务超时")
```

#### 1.2 待处理任务执行
```python
# 获取下一个待处理任务
pending_task = TaskDao.get_next_pending_task()

# 在线程池中执行任务
await loop.run_in_executor(
    None,
    process_channel_videos_background,
    task_id, channel_id, channel_url, max_videos
)
```

### 涉及的数据库表
- **ytb_tasks**: 任务管理表
  - `id`: 任务ID
  - `task_type`: 任务类型
  - `status`: 任务状态 (pending/running/success/failed)
  - `channel_id`: 频道ID
  - `parameters`: 任务参数(JSON)
  - `result`: 执行结果(JSON)
  - `error_message`: 错误信息
  - `created_at/updated_at`: 时间戳

### 错误处理机制
- **数据库连接池监控**: 连续失败5次后停止调度器
- **任务超时处理**: 超过5分钟未更新的任务标记为失败
- **异常捕获**: 捕获所有执行异常并记录日志

---

## 2. 统计数据更新任务 (statistics_job.py)

### 功能描述
每日凌晨2:00执行，基于频道快照数据计算多维度统计指标。

### 执行时间
- **执行时间**: 每天凌晨 2:00
- **数据范围**: 处理前一天的数据

### 核心逻辑流程

#### 2.1 定时调度
```python
# 计算到下一个凌晨 2:00 的等待时间
target_time = now.replace(hour=2, minute=0, second=0, microsecond=0)
if now.hour >= 2:
    target_time = target_time + timedelta(days=1)

await asyncio.sleep(seconds_until_target)
```

#### 2.2 统计数据处理
```python
# 处理前一天的数据
target_date = datetime.now() - timedelta(days=1)
success = ChannelStatsUpdater.update_all_channel_statistics(target_date)
```

### 统计维度计算

#### 2.2.1 日度统计
- **数据来源**: 今天最新快照 vs 昨天最早快照
- **计算指标**: 订阅数变化、观看数变化、增长率

#### 2.2.2 周度统计
- **数据来源**: 本周最新快照 vs 上周最早快照
- **时间计算**: 使用ISO周历

#### 2.2.3 月度统计
- **数据来源**: 本月最新快照 vs 上月最早快照
- **跨年处理**: 支持跨年月份计算

#### 2.2.4 季度统计
- **季度计算**: `quarter = (month - 1) // 3 + 1`
- **数据来源**: 季度最新 vs 季度开始快照

#### 2.2.5 年度统计
- **数据来源**: 年末最新快照 vs 年初最早快照

### 涉及的数据库表

#### 输入表
- **ytb_channels**: 频道基础信息
- **ytb_channel_stats_snapshots**: 频道统计快照
  - `channel_id`: 频道ID
  - `view_count`: 观看次数快照
  - `subscriber_count`: 订阅者数量快照
  - `video_count`: 视频数量快照
  - `snapshot_at`: 快照时间

#### 输出表
- **ytb_channel_time_statistics**: 时间统计表
  - `channel_id`: 频道ID
  - `period_type`: 统计周期(day/week/month/quarter/year)
  - `period_start/period_end`: 统计周期时间范围
  - `start_subscribers/end_subscribers`: 订阅数起止值
  - `subscriber_change/subscriber_growth_rate`: 订阅数变化和增长率
  - `start_views/end_views`: 观看数起止值
  - `view_change/view_growth_rate`: 观看数变化和增长率
  - `year/quarter/month/week/day`: 时间维度字段

### 增长率计算公式
```python
def calculate_growth_rate(start_value: float, end_value: float) -> float:
    if start_value == 0:
        return 100.0 if end_value > 0 else 0.0
    return ((end_value - start_value) / start_value) * 100
```

---

## 3. 频道信息更新任务 (updateChanne_job.py)

### 功能描述
API方式更新频道基础信息和统计快照，每日执行一次。

### 执行时间
- **执行时间**: 每天凌晨 0:10
- **更新方式**: YouTube Data API v3

### 核心逻辑流程

#### 3.1 频道快照更新
```python
# 调用频道快照更新服务
success = update_channel_info_snapshot()
```

#### 3.2 快照去重逻辑
```python
# 检查当天是否已有快照
latest_snapshot_date = ChannelDao.get_latest_snapshot_date(channel_id)
today = date.today()
if latest_snapshot_date == today:
    logger.info("今日快照已存在，无需更新")
    continue
```

### 涉及的数据库表
- **ytb_channels**: 频道基础信息更新
- **ytb_channel_stats_snapshots**: 新增每日快照数据

### API调用流程
1. 调用YouTube Data API获取频道信息
2. 更新频道基础信息到`ytb_channels`表
3. 保存当前统计数据快照到`ytb_channel_stats_snapshots`表

---

## 4. 视频数据更新任务 (updateVideos_job.py)

### 功能描述
API方式更新频道内视频数据，获取最近50个视频的详细信息。

### 执行时间
- **执行时间**: 每天凌晨 1:00
- **更新方式**: YouTube Data API v3

### 核心逻辑流程

#### 4.1 主处理函数
```python
# 调用YouTube爬虫主函数
youtube_main()
```

#### 4.2 频道视频处理
```python
# 获取所有频道
channels = ChannelDao.get_all_channels()

# 处理每个频道的视频
for channel in channels:
    process_channel(youtube_spider, channel_id)
```

### 涉及的数据库表
- **ytb_channels**: 频道信息
- **ytb_videos**: 视频主表
- **ytb_video_stats_snapshots**: 视频统计快照
- **ytb_raw_fetch**: 原始API响应数据

### API处理流程
1. 获取频道上传播放列表ID
2. 获取播放列表中的视频ID列表(最多50个)
3. 逐个获取视频详细信息
4. 保存视频数据和统计快照

---

## 5. 频道和视频全量更新任务 (updateChannelAll_job.py)

### 功能描述
yt-dlp方式更新所有频道的历史数据和视频数据，功能最全面。

### 执行时间
- **执行时间**: 每天凌晨 3:00
- **更新方式**: yt-dlp工具

### 核心逻辑流程

#### 5.1 频道历史数据更新
```python
# 更新所有频道历史数据
yt_dlp_all_channel_main()
```

#### 5.2 频道视频数据更新
```python
# 更新所有频道视频数据
yt_dlp_all_videos_main()
```

### yt-dlp处理流程

#### 5.2.1 频道数据获取
```python
command = [
    'yt-dlp',
    '--skip-download',
    '-j',                    # JSON格式输出
    '--flat-playlist',       # 快速获取列表
    '--ignore-errors',
    channel_url
]
```

#### 5.2.2 视频数据获取
```python
# 获取发布时间为空的视频
videos = youtube_dao.get_video_by_published_at_is_null(channel_id)

# 逐个更新视频详细信息
for video in videos:
    get_single_video_json_and_save(video_url, youtube_dao, channel_dao)
```

### 涉及的数据库表
- **ytb_channels**: 频道信息
- **ytb_videos**: 视频主表
- **ytb_video_stats_snapshots**: 视频统计快照

### 特殊处理
- **18+视频**: 需要cookie才能获取完整信息
- **会员专属视频**: 自动跳过处理
- **私密视频**: 自动跳过处理

---

## 6. 临时文件清理任务 (cleanup_job.py) - 已禁用

### 功能描述
清理下载临时文件，删除1小时前的临时文件夹。

### 执行时间
- **执行间隔**: 每小时执行一次
- **清理规则**: 删除创建时间超过1小时的临时目录

### 核心逻辑
```python
cutoff = now - timedelta(hours=1)
for item in os.listdir(TEMP_DIR):
    created_time = datetime.fromtimestamp(os.path.getctime(item_path))
    if created_time < cutoff:
        shutil.rmtree(item_path)
```

**注意**: 此任务在`__init__.py`中已被注释禁用。

---

## 错误处理和监控

### 通用错误处理机制
1. **数据库连接池监控**: 所有任务都监控连接池状态
2. **连续失败计数**: 连续失败3次后停止任务
3. **详细日志记录**: 记录所有操作和错误信息
4. **任务状态追踪**: 通过`ytb_tasks`表追踪任务执行状态

### 监控指标
- 任务执行成功率
- 数据库连接池状态
- API调用成功率
- 数据处理量统计

### 日志级别控制
- 生产环境: 只显示ERROR级别日志
- 开发环境: 显示INFO级别日志
- 通过`ENVIRONMENT`环境变量控制

---

## 数据流向图

```
YouTube API/yt-dlp
        ↓
频道信息更新 (0:10)
        ↓
ytb_channels + ytb_channel_stats_snapshots
        ↓
视频数据更新 (1:00)
        ↓
ytb_videos + ytb_video_stats_snapshots
        ↓
统计数据计算 (2:00)
        ↓
ytb_channel_time_statistics
        ↓
全量数据更新 (3:00)
        ↓
数据完整性保障
```

这个定时任务系统确保了YouTube频道和视频数据的完整性、时效性和准确性，为后续的数据分析和统计提供了可靠的数据基础。

---

## 任务依赖关系和执行顺序

### 时间线安排
```
00:10 - updateChanne_job      (频道信息更新)
01:00 - updateVideos_job      (视频数据更新-API方式)
02:00 - statistics_job        (统计数据计算)
03:00 - updateChannelAll_job  (全量数据更新-yt-dlp方式)
持续  - task_scheduler        (用户任务调度)
```

### 依赖关系
1. **statistics_job** 依赖 **updateChanne_job** 产生的快照数据
2. **updateVideos_job** 需要频道信息已存在
3. **updateChannelAll_job** 作为数据完整性保障，独立执行

---

## 性能优化和配置

### API配额管理
- **YouTube Data API**: 每日配额限制
- **请求频率控制**: 每次请求间隔0.5秒
- **批量处理**: 支持批量视频数据处理
- **错误重试**: 支持API请求失败重试

### yt-dlp优化配置
```python
# 频道视频列表获取
command = [
    'yt-dlp',
    '--skip-download',      # 不下载视频文件
    '-j',                   # JSON格式输出
    '--flat-playlist',      # 快速获取播放列表
    '--ignore-errors',      # 忽略单个视频错误
    '--no-playlist',        # 单视频模式
    channel_url
]
```

### 数据库优化
- **连接池管理**: 自动管理数据库连接
- **批量操作**: 支持批量插入和更新
- **索引优化**: 关键字段建立索引
- **软删除**: 使用`deleted_at`字段实现软删除

---

## 故障排查指南

### 常见问题和解决方案

#### 1. 数据库连接池耗尽
**症状**: 日志显示"connection pool exhausted"
**解决方案**:
- 检查数据库连接配置
- 增加连接池大小
- 重启应用程序

#### 2. YouTube API配额超限
**症状**: API返回403错误
**解决方案**:
- 检查API密钥配额使用情况
- 等待配额重置(通常为每日重置)
- 考虑使用多个API密钥轮换

#### 3. yt-dlp命令失败
**症状**: "yt-dlp命令未找到"
**解决方案**:
- 确保yt-dlp已正确安装
- 检查系统PATH环境变量
- 更新yt-dlp到最新版本

#### 4. 任务长时间运行
**症状**: 任务状态长时间为"running"
**解决方案**:
- 检查任务超时设置(默认5分钟)
- 查看详细日志确定卡住的步骤
- 手动重置任务状态

### 日志分析
```bash
# 查看特定任务的日志
grep "task_scheduler" logs/app.log

# 查看错误日志
grep "ERROR" logs/error.log

# 查看统计任务执行情况
grep "statistics_job" logs/app.log
```

---

## 扩展和自定义

### 添加新的定时任务
1. 在`src/jobs/`目录创建新的任务文件
2. 实现异步任务函数和启动函数
3. 在`__init__.py`中注册新任务
4. 添加错误处理和日志记录

### 自定义执行时间
```python
# 修改执行时间示例
target_time = now.replace(hour=4, minute=30, second=0, microsecond=0)
```

### 添加新的统计维度
1. 在`ChannelStatsUpdater`类中添加新的处理函数
2. 更新数据库表结构
3. 修改`update_channel_statistics`函数调用新处理函数

---

## 监控和告警建议

### 关键监控指标
- 任务执行成功率
- 数据更新时效性
- API调用成功率
- 数据库性能指标

### 告警设置建议
- 连续任务失败超过3次
- 数据库连接池使用率超过80%
- API配额使用率超过90%
- 磁盘空间不足

### 健康检查端点
建议添加健康检查API端点，用于监控系统状态：
```python
@app.get("/health/jobs")
async def jobs_health_check():
    return {
        "task_scheduler": "running",
        "statistics_job": "scheduled",
        "last_update": datetime.now().isoformat()
    }
```

---

## 数据表结构详细说明

### 核心数据表关系图
```
ytb_channels (频道主表)
    ↓ 1:N
ytb_channel_stats_snapshots (频道统计快照)
    ↓ 聚合计算
ytb_channel_time_statistics (时间统计表)

ytb_channels (频道主表)
    ↓ 1:N
ytb_videos (视频主表)
    ↓ 1:N
ytb_video_stats_snapshots (视频统计快照)

ytb_tasks (任务管理表)
    ↓ 1:N
ytb_raw_fetch (原始API数据)
```

### 表结构详细定义

#### ytb_tasks (任务管理表)
```sql
CREATE TABLE ytb_tasks (
    id BIGSERIAL PRIMARY KEY,
    task_type VARCHAR(50) NOT NULL,           -- 任务类型
    status VARCHAR(20) NOT NULL DEFAULT 'pending', -- pending/running/success/failed
    channel_id VARCHAR(50),                   -- 关联频道ID
    parameters JSONB,                         -- 任务参数
    result JSONB,                            -- 执行结果
    error_message TEXT,                      -- 错误信息
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    deleted_at TIMESTAMPTZ
);
```

#### ytb_channel_stats_snapshots (频道统计快照表)
```sql
CREATE TABLE ytb_channel_stats_snapshots (
    id BIGSERIAL PRIMARY KEY,
    channel_id VARCHAR(50) NOT NULL REFERENCES ytb_channels(channel_id),
    view_count BIGINT NOT NULL DEFAULT 0,        -- 快照时的观看次数
    subscriber_count INTEGER NOT NULL DEFAULT 0, -- 快照时的订阅者数量
    video_count INTEGER NOT NULL DEFAULT 0,      -- 快照时的视频数量
    snapshot_at TIMESTAMPTZ NOT NULL DEFAULT NOW(), -- 快照时间
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ
);
```

#### ytb_channel_time_statistics (时间统计表)
```sql
CREATE TABLE ytb_channel_time_statistics (
    id BIGSERIAL PRIMARY KEY,
    channel_id VARCHAR(50) NOT NULL REFERENCES ytb_channels(channel_id),
    period_type VARCHAR(20) NOT NULL,            -- day/week/month/quarter/year
    period_start DATE NOT NULL,                  -- 统计周期开始日期
    period_end DATE NOT NULL,                    -- 统计周期结束日期

    -- 订阅数据
    start_subscribers INTEGER NOT NULL DEFAULT 0,
    end_subscribers INTEGER NOT NULL DEFAULT 0,
    subscriber_change INTEGER NOT NULL DEFAULT 0,
    subscriber_growth_rate NUMERIC(6,2),

    -- 观看数据
    start_views BIGINT NOT NULL DEFAULT 0,
    end_views BIGINT NOT NULL DEFAULT 0,
    view_change BIGINT NOT NULL DEFAULT 0,
    view_growth_rate NUMERIC(6,2),

    -- 时间维度
    year INTEGER,
    quarter INTEGER,
    month INTEGER,
    week INTEGER,
    day INTEGER,

    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ
);
```

这个完整的定时任务系统为YouTube数据采集和分析提供了强大的自动化能力，具有良好的可扩展性、可维护性和监控能力。

---

## 任务依赖关系和执行顺序

### 时间线安排
```
00:10 - updateChanne_job      (频道信息更新)
01:00 - updateVideos_job      (视频数据更新-API方式)
02:00 - statistics_job        (统计数据计算)
03:00 - updateChannelAll_job  (全量数据更新-yt-dlp方式)
持续  - task_scheduler        (用户任务调度)
```

### 依赖关系
1. **statistics_job** 依赖 **updateChanne_job** 产生的快照数据
2. **updateVideos_job** 需要频道信息已存在
3. **updateChannelAll_job** 作为数据完整性保障，独立执行

---

## 性能优化和配置

### API配额管理
- **YouTube Data API**: 每日配额限制
- **请求频率控制**: 每次请求间隔0.5秒
- **批量处理**: 支持批量视频数据处理
- **错误重试**: 支持API请求失败重试

### yt-dlp优化配置
```python
# 频道视频列表获取
command = [
    'yt-dlp',
    '--skip-download',      # 不下载视频文件
    '-j',                   # JSON格式输出
    '--flat-playlist',      # 快速获取播放列表
    '--ignore-errors',      # 忽略单个视频错误
    '--no-playlist',        # 单视频模式
    channel_url
]
```

### 数据库优化
- **连接池管理**: 自动管理数据库连接
- **批量操作**: 支持批量插入和更新
- **索引优化**: 关键字段建立索引
- **软删除**: 使用`deleted_at`字段实现软删除

---

## 故障排查指南

### 常见问题和解决方案

#### 1. 数据库连接池耗尽
**症状**: 日志显示"connection pool exhausted"
**解决方案**:
- 检查数据库连接配置
- 增加连接池大小
- 重启应用程序

#### 2. YouTube API配额超限
**症状**: API返回403错误
**解决方案**:
- 检查API密钥配额使用情况
- 等待配额重置(通常为每日重置)
- 考虑使用多个API密钥轮换

#### 3. yt-dlp命令失败
**症状**: "yt-dlp命令未找到"
**解决方案**:
- 确保yt-dlp已正确安装
- 检查系统PATH环境变量
- 更新yt-dlp到最新版本

#### 4. 任务长时间运行
**症状**: 任务状态长时间为"running"
**解决方案**:
- 检查任务超时设置(默认5分钟)
- 查看详细日志确定卡住的步骤
- 手动重置任务状态

### 日志分析
```bash
# 查看特定任务的日志
grep "task_scheduler" logs/app.log

# 查看错误日志
grep "ERROR" logs/error.log

# 查看统计任务执行情况
grep "statistics_job" logs/app.log
```

---

## 扩展和自定义

### 添加新的定时任务
1. 在`src/jobs/`目录创建新的任务文件
2. 实现异步任务函数和启动函数
3. 在`__init__.py`中注册新任务
4. 添加错误处理和日志记录

### 自定义执行时间
```python
# 修改执行时间示例
target_time = now.replace(hour=4, minute=30, second=0, microsecond=0)
```

### 添加新的统计维度
1. 在`ChannelStatsUpdater`类中添加新的处理函数
2. 更新数据库表结构
3. 修改`update_channel_statistics`函数调用新处理函数

---

## 监控和告警建议

### 关键监控指标
- 任务执行成功率
- 数据更新时效性
- API调用成功率
- 数据库性能指标

### 告警设置建议
- 连续任务失败超过3次
- 数据库连接池使用率超过80%
- API配额使用率超过90%
- 磁盘空间不足

### 健康检查端点
建议添加健康检查API端点，用于监控系统状态：
```python
@app.get("/health/jobs")
async def jobs_health_check():
    return {
        "task_scheduler": "running",
        "statistics_job": "scheduled",
        "last_update": datetime.now().isoformat()
    }
```

这个定时任务系统为YouTube数据采集和分析提供了完整的自动化解决方案，具有良好的可扩展性和可维护性。
