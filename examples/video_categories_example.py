#!/usr/bin/env python3
"""
YouTube视频分类功能示例
基于您提供的代码实现的FastAPI版本

使用方法：
1. 确保在.env文件中配置了YOUTUBE_API_KEY
2. 运行: python examples/video_categories_example.py
"""

import sys
import os
from dotenv import load_dotenv

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 加载环境变量
load_dotenv()

from src.services.video_category_service import (
    get_channel_video_ids,
    get_video_categories, 
    get_category_titles,
    get_channel_video_categories
)

# 配置参数
API_KEY = os.getenv('YOUTUBE_API_KEY', '')
CHANNEL_ID = 'UCXuqSBlHAE6Xw-yeJA0Tunw'  # 示例频道ID (Linus Tech Tips)

def main():
    """
    主函数 - 复现您提供的功能
    """
    if not API_KEY:
        print("错误: 未配置YouTube API密钥")
        print("请在.env文件中设置YOUTUBE_API_KEY")
        return
    
    print("YouTube视频分类获取示例")
    print("="*50)
    print(f"频道ID: {CHANNEL_ID}")
    print(f"API密钥: {API_KEY[:10]}...")
    print()
    
    try:
        # 方法1: 使用封装好的函数（推荐）
        print("方法1: 使用封装函数")
        print("-"*30)
        
        result = get_channel_video_categories(
            channel_id=CHANNEL_ID,
            max_results=5,
            region_code='US'
        )
        
        print(f"频道ID: {result['channel_id']}")
        print(f"获取到 {result['total_count']} 个视频")
        print()
        
        for video in result['videos']:
            print(f"视频ID: {video['video_id']}")
            print(f"分类ID: {video['category_id']}")
            print(f"分类名称: {video['category_title']}")
            print("-"*20)
        
        print()
        
        # 方法2: 分步调用（类似您的原始代码）
        print("方法2: 分步调用（类似原始代码）")
        print("-"*30)
        
        # 1. 获取频道下的视频ID
        video_ids = get_channel_video_ids(CHANNEL_ID, max_results=5)
        print(f"获取到的视频ID: {video_ids}")
        
        # 2. 获取视频的分类ID
        video_categories = get_video_categories(video_ids)
        print(f"视频分类信息: {video_categories}")
        
        # 3. 获取分类ID对应的分类名称
        category_ids = [cat_info['category_id'] for cat_info in video_categories]
        category_titles = get_category_titles(category_ids, region_code='US')
        print(f"分类标题映射: {category_titles}")
        
        print()
        print("详细结果:")
        for video_info in video_categories:
            video_id = video_info['video_id']
            category_id = video_info['category_id']
            category_title = category_titles.get(category_id, "Unknown")
            
            print(f'Video ID: {video_id}, Category ID: {category_id}, Category: {category_title}')
        
    except Exception as e:
        print(f"发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

def test_different_channels():
    """测试不同的频道"""
    test_channels = [
        ('UCXuqSBlHAE6Xw-yeJA0Tunw', 'Linus Tech Tips'),
        ('UC_x5XG1OV2P6uZZ5FSM9Ttw', 'Google Developers'),
        ('UCsBjURrPoezykLs9EqgamOA', 'Fireship'),
    ]
    
    print("\n" + "="*50)
    print("测试不同频道")
    print("="*50)
    
    for channel_id, channel_name in test_channels:
        print(f"\n测试频道: {channel_name} ({channel_id})")
        print("-"*40)
        
        try:
            result = get_channel_video_categories(
                channel_id=channel_id,
                max_results=3,
                region_code='US'
            )
            
            print(f"获取到 {result['total_count']} 个视频")
            
            for video in result['videos']:
                print(f"  - {video['video_id']}: {video['category_title']}")
                
        except Exception as e:
            print(f"  错误: {str(e)}")

def test_different_regions():
    """测试不同地区的分类"""
    regions = ['US', 'CN', 'JP', 'GB', 'DE']
    
    print("\n" + "="*50)
    print("测试不同地区分类")
    print("="*50)
    
    for region in regions:
        print(f"\n地区: {region}")
        print("-"*20)
        
        try:
            result = get_channel_video_categories(
                channel_id=CHANNEL_ID,
                max_results=2,
                region_code=region
            )
            
            categories = set()
            for video in result['videos']:
                categories.add(video['category_title'])
            
            print(f"分类: {', '.join(categories)}")
            
        except Exception as e:
            print(f"错误: {str(e)}")

if __name__ == '__main__':
    main()
    
    # 可选: 测试其他功能
    # test_different_channels()
    # test_different_regions()
