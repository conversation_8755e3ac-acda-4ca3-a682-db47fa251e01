# reyoutube-fastapi:

## 介绍：
1. 获取YouTube所有视频和频道的数据，做成数据分析平台，对标网址viewstate.com
2. 服务端，优先数据采集，通过官方API方式，会有额度问题，后期采用ytb-dl方式补齐；
3. 差异化功能设计

### 场景模拟：优先MVP功能
1. 获取频道所有数据
2. 定期更新最新50条数据情况
3. 频道没有，高并发获取历史数据情况，添加任务队列
4. 频道没有，添加数据接口
5. 频道有，但是数据不全，添加历史数据接口
6. 频道有，但是数据不新，添加更新数据接口
7. 频道有，数据有，综合数据对比，月新增订阅量，月新增播放量，月新增视频量，月新增收益，这些数据如何获取
8. 统计数据：
    过去一个月的播放量，播放量环比
    过去一个月的订阅数，订阅数环比
    过去一个月的收益，收益环比
    每日平均 订阅数，观看数，收益的变化 数量
    每月平均

9. 短视频数据获取
10.预测数据
11.类似频道



## 环境配置

本项目使用环境变量进行配置。请按照以下步骤设置：

1. 复制`.env-example`文件并重命名为`.env`
2. 在`.env`文件中填写实际的配置值

### 必要的环境变量

- `DB_HOST`: 数据库主机
- `DB_PORT`: 数据库端口
- `DB_USER`: 数据库用户名
- `DB_PASSWORD`: 数据库密码
- `DB_NAME`: 数据库名称
- `YOUTUBE_API_KEY`: YouTube API密钥

### 可选的环境变量

- `DB_MIN_CONNECTIONS`: 数据库最小连接数（默认2）
- `DB_MAX_CONNECTIONS`: 数据库最大连接数（默认10）
- `NOTION_API_KEY`: Notion API密钥（如果使用Notion集成）
- `NOTION_DATABASE_ID`: Notion数据库ID（如果使用Notion集成）

## 本地运行:
### 标准命令：
1. uv sync
2. source .venv/bin/activate
3. uv run -m uvicorn src.app:app --reload

### 旧定时命令：
1. 运行: python src/main.py --schedule 
2. 立即运行: python src/main.py --now
3. 默认-定时运行: python src/main.py
4. fastapi启动：
python start_api.py
python start_api.py 9000

最新启动命令：
python -m uvicorn src.app:app --reload

uv启动命令：
uv run -m uvicorn src.app:app --reload

### 常用URL调用请求：
```shell
# 更新视频评论量和点赞量都是0的视频数据
curl -X 'PUT' 'http://127.0.0.1:8000/api/channel/video/update_video_comment_and_like_count_zero' -H 'accept: application/json' --connect-timeout 3600 --max-time 3600

# 更新视频发布时间为空的视频数据
curl -X 'PUT' \
  'http://127.0.0.1:8000/api/channel/video/update_video_by_yt_dlp' \
  -H 'accept: application/json'
```


# 定时任务：
#### 手动记录：
1. updateChanne_job更新频道的每日数据之后， statistics_job才能统计数据到时间表ytb_channel_time_statistics，前端用于展示。 

2. 相关sql：
```shell
-- 查询频道快照
SELECT * from ytb_channel_stats_snapshots order by created_at desc  LIMIT 10
SELECT * from ytb_channel_stats_snapshots WHERE channel_id='UCX6OQ3DkcsbYNE6H8uQQuVA' order by created_at desc  LIMIT 10
-- 查询视频快照
SELECT * from ytb_video_stats_snapshots order by created_at desc LIMIT 10

-- 查询时间快照：
SELECT * from ytb_channel_time_statistics order by created_at desc LIMIT 10
SELECT * from ytb_channel_time_statistics WHERE channel_id='UCX6OQ3DkcsbYNE6H8uQQuVA' and period_type ='day' order by created_at desc LIMIT 10
```




# TODO：
1. 批量新增用户频道：
    - API受限，需要轮询APIkey
2. 单条用户频道新增实现-Done
    - 根据视频找到用户
    - 根据用户id找到频道ID
    - 支持URL或者自定义ID添加视频
3. 获取频道视频避免重复获取-Done
    - 重复的自动更新数据，同时建立快照数据
4. 获取频道一年的视频数据-Done
    - 需要保证yt-dlp过cookie验证
5. 获取频道所有的视频数据-Done
    - 同上
6. 频道分月数据获取
    - 同上
7. 代码拆分定时任务和接口-Done
8. 每月数据功能实现，获取；-Done
    - test_statistics.py 验证数据获取
9. 增加API方式获取接口 和 yt-dlp方式接口 两种不同的方式；-Done
    - 需要保证yt-dlp跳过cookis验证
10. 优化后，测试所有的接口，api模式，yt-dlp模式-Done
    - history-recent 测试OK API方式获取频道和最近50条视频数据；同步最新频道信息和视频信息；
    - test_yt_dlp_all 获取频道所有视频
    - test_yt_dlp_single 获取频道所有视频完善数据


### 服务器部署：-Done
1. 搭建python，uv 统一管理pip环境-不同环境因为数据库链接是封闭的。 
2. 安装yt-dlp
3. 服务验证，数据库链接
4. 当前数据导出导入生产环境
5. 验证服务端接口
6. 配置前端链接数据
7. 部署域名nginx 
8. 验证页面，修改页面显示问题等等
9. 后期功能：导入频道id，定时统计视频和流量信息，异常问题处理机制；


### 问题：
1. yt-dlp年龄限制的问题-Done
    - 用cookies解决
2. db客户端同步的问题，改为异步-Done
3. 会员的视频是否可以获取-Done
    - 不可以
