import uvicorn
import sys
import os
from pathlib import Path
from dotenv import load_dotenv

# 添加项目根目录到 Python 路径
project_root = str(Path(__file__).parent.parent)
if project_root not in sys.path:
    sys.path.append(project_root)

from src.app import app
from src.utils.logger import setup_logging_from_config

# 加载环境变量
load_dotenv()

if __name__ == "__main__":
    # 设置日志
    setup_logging_from_config()
    
    # 运行应用
    port = int(os.getenv("PORT", "8100"))
    uvicorn.run("src.app:app", host="0.0.0.0", port=port, reload=True)
