import logging
import logging.config
import yaml
import os
from pathlib import Path
from datetime import datetime
from copy import deepcopy

def setup_logging():
    """设置基础日志配置"""
    # 创建日志格式
    log_format = '%(asctime)s [%(levelname)s] %(name)s: %(message)s'
    date_format = '%Y-%m-%d %H:%M:%S'
    
    # 基础配置
    logging.basicConfig(
        level=logging.INFO,
        format=log_format,
        datefmt=date_format
    )
    
    # 减少一些库的日志级别
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('schedule').setLevel(logging.WARNING)

def get_logger(name):
    """获取指定名称的日志记录器"""
    logger = logging.getLogger(name)

    # 检查是否已经通过setup_logging_from_config配置过
    root_logger = logging.getLogger()
    if root_logger.handlers:
        # 如果根logger已经有处理器，说明已经通过YAML配置过了
        # 直接返回logger，不添加额外的处理器
        return logger

    # 如果没有配置过，使用基本配置（向后兼容）
    if logger.handlers:
        return logger

    # 创建文件处理器
    log_dir = Path('logs')
    log_dir.mkdir(exist_ok=True)

    file_handler = logging.FileHandler(
        log_dir / f'{name}.log',
        encoding='utf-8'
    )

    # 创建控制台处理器
    console_handler = logging.StreamHandler()

    # 设置格式
    formatter = logging.Formatter(
        '%(asctime)s [%(levelname)s] %(name)s: %(message)s',
        '%Y-%m-%d %H:%M:%S'
    )
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    # 添加处理器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    # 确保日志级别设置正确
    logger.setLevel(logging.INFO)

    return logger

def setup_logger(name: str = 'youtube_parser') -> logging.Logger:
    """设置日志记录器"""
    try:
        # 确保日志目录存在
        log_dir = Path('logs')
        log_dir.mkdir(exist_ok=True)
        
        # 加载日志配置
        with open('config/logging.yaml', 'r') as f:
            config = yaml.safe_load(f)
            
        logging.config.dictConfig(config)
        return logging.getLogger(name)
        
    except Exception as e:
        # 如果配置加载失败，使用基本配置
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('logs/parser.log', encoding='utf8')
            ]
        )
        logger = logging.getLogger(name)
        logger.error(f"加载日志配置失败: {str(e)}")
        return logger

def setup_logging_from_config():
    """
    根据环境变量设置日志配置
    生产环境只显示ERROR级别日志，开发环境显示INFO级别日志
    """
    try:
        # 获取环境变量
        environment = os.getenv('ENVIRONMENT', 'development').lower()

        # 确保日志目录存在
        log_dir = Path('logs')
        log_dir.mkdir(exist_ok=True)

        # 加载基础日志配置
        config_path = Path('config/logging.yaml')
        if not config_path.exists():
            # 如果配置文件不存在，使用基本配置
            setup_logging()
            return

        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)

        # 根据环境调整日志级别
        if environment == 'production':
            # 生产环境：只显示ERROR级别日志
            log_level = 'ERROR'
            print(f"生产环境模式：日志级别设置为 {log_level}")
        else:
            # 开发环境：显示INFO级别日志
            log_level = 'INFO'
            print(f"开发环境模式：日志级别设置为 {log_level}")

        # 动态修改配置中的日志级别
        config = _adjust_log_levels(config, log_level)

        # 应用配置
        logging.config.dictConfig(config)

        # 减少一些库的日志级别（避免过多噪音）
        logging.getLogger('urllib3').setLevel(logging.WARNING)
        logging.getLogger('requests').setLevel(logging.WARNING)
        logging.getLogger('schedule').setLevel(logging.WARNING)
        logging.getLogger('asyncio').setLevel(logging.WARNING)

        print(f"日志配置加载成功，环境: {environment}, 级别: {log_level}")

    except Exception as e:
        print(f"加载日志配置失败: {str(e)}")
        # 回退到基本配置
        setup_logging()

def _adjust_log_levels(config, target_level):
    """
    调整日志配置中的级别设置
    """
    config = deepcopy(config)

    # 调整处理器级别
    if 'handlers' in config:
        for handler_name, handler_config in config['handlers'].items():
            if handler_name == 'error_file':
                # error_file处理器始终保持ERROR级别
                continue
            else:
                # 其他处理器根据环境调整
                handler_config['level'] = target_level

    # 调整记录器级别
    if 'loggers' in config:
        for logger_name, logger_config in config['loggers'].items():
            logger_config['level'] = target_level

    # 调整根记录器级别
    if 'root' in config:
        config['root']['level'] = target_level

    return config