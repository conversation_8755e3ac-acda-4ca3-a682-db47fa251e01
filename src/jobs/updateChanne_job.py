import asyncio
from datetime import datetime, timedelta
from ..utils.logger import get_logger
from src.services.channel_service import update_channel_info_daily,update_channel_info_snapshot
# 设置日志
logger = get_logger('update_channel_job')

async def update_channel_job():
    """
    API方式-频道快照数据更新: 更新频道最新的订阅数量，浏览数量信息，每日更新，更新一次，每日零点十分触发更新
    增强版：支持重试机制和数据完整性检查
    """
    db_error_count = 0  # 数据库连接错误计数
    max_db_errors = 3   # 最大连续数据库错误次数
    retry_count = 0     # 重试计数
    max_retries = 3     # 最大重试次数

    while True:
        now = datetime.now()

        # 计算到下一个凌晨 0:10 的秒数
        target_time = now.replace(hour=0, minute=10, second=0, microsecond=0)
        if now.hour >= 0:
            target_time = target_time + timedelta(days=1)

        # 计算需要等待的秒数
        seconds_until_target = (target_time - now).total_seconds()

        logger.info(f"updateChanne_job：频道数据更新计划在 {target_time.strftime('%Y-%m-%d %H:%M:%S')} 运行，"
                  f"等待 {seconds_until_target:.2f} 秒")

        # 等待到目标时间
        await asyncio.sleep(seconds_until_target)

        # 运行频道数据更新，支持重试机制
        success = False
        retry_count = 0

        while not success and retry_count <= max_retries:
            try:
                if retry_count > 0:
                    logger.info(f"频道数据更新重试第 {retry_count} 次")
                    await asyncio.sleep(300)  # 重试前等待5分钟
                else:
                    logger.info("开始执行频道数据每日更新")

                success = update_channel_info_snapshot()

                if success:
                    logger.info("频道数据更新成功")
                    # 成功执行，重置错误计数
                    db_error_count = 0
                    retry_count = 0

                    # 执行数据完整性检查
                    await asyncio.sleep(60)  # 等待1分钟让数据稳定
                    await perform_data_integrity_check()

                else:
                    logger.warning(f"频道数据更新失败，准备重试 (尝试 {retry_count + 1}/{max_retries + 1})")
                    retry_count += 1

            except Exception as e:
                error_msg = str(e)
                logger.error(f"频道数据更新出错: {error_msg}")
                retry_count += 1

                # 检查是否是数据库连接池耗尽错误
                if "connection pool exhausted" in error_msg or "获取数据库连接失败" in error_msg:
                    db_error_count += 1
                    logger.error(f"数据库连接池耗尽，错误次数: {db_error_count}/{max_db_errors}")

                    if db_error_count >= max_db_errors:
                        logger.critical(f"数据库连接池连续失败 {max_db_errors} 次，updateChanne_job 停止运行")
                        logger.critical("请检查数据库连接配置和连接池设置，或重启应用程序")
                        return  # 终止任务

        # 如果所有重试都失败了
        if not success:
            logger.critical(f"频道数据更新连续失败 {max_retries + 1} 次，可能存在严重问题")
            # 发送紧急告警
            await send_critical_alert("频道数据更新", f"连续失败 {max_retries + 1} 次")

        # 睡眠一小时，避免因时间计算误差导致短时间内重复执行
        await asyncio.sleep(3600)

async def perform_data_integrity_check():
    """执行数据完整性检查"""
    try:
        from src.utils.channel_stats_updater import ChannelStatsUpdater

        logger.info("开始执行数据完整性检查")
        integrity_result = ChannelStatsUpdater.check_snapshot_data_integrity(days=3)

        if 'error' in integrity_result:
            logger.error(f"数据完整性检查失败: {integrity_result['error']}")
            await send_alert("数据完整性检查", f"检查失败: {integrity_result['error']}")
            return

        missing_snapshots = integrity_result.get('missing_snapshots', [])
        missing_statistics = integrity_result.get('missing_statistics', [])

        if missing_snapshots:
            logger.warning(f"发现 {len(missing_snapshots)} 条缺失的快照数据")
            await send_alert("快照数据缺失", f"缺失 {len(missing_snapshots)} 条快照数据")

        if missing_statistics:
            logger.warning(f"发现 {len(missing_statistics)} 条缺失的统计数据")
            await send_alert("统计数据缺失", f"缺失 {len(missing_statistics)} 条统计数据")

        if not missing_snapshots and not missing_statistics:
            logger.info("数据完整性检查通过")

    except Exception as e:
        logger.error(f"数据完整性检查异常: {str(e)}")
        await send_alert("数据完整性检查异常", str(e))

async def send_alert(alert_type: str, message: str):
    """发送告警通知"""
    try:
        from src.services.alert_service import send_warning_alert
        await send_warning_alert(alert_type, message, "频道信息更新任务")

    except Exception as e:
        logger.error(f"发送告警失败: {str(e)}")

async def send_critical_alert(component: str, message: str):
    """发送紧急告警"""
    try:
        from src.services.alert_service import send_critical_alert as send_critical
        await send_critical(f"{component}严重故障", message, "频道信息更新任务")

    except Exception as e:
        logger.error(f"发送紧急告警失败: {str(e)}")

async def start():
    """启动频道信息更新任务"""
    logger.info("启动更新频道信息任务")
    asyncio.create_task(update_channel_job())
    