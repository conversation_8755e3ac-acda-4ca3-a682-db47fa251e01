import os
import shutil
import asyncio
from datetime import datetime, timedelta
from ..utils.logger import get_logger
from ..utils.download_utils import TEMP_DIR
from ..spider.spider_youtube import youtube_main
# 设置日志
logger = get_logger('update_videos_job')

async def update_videos_job():
    """
    API方式：更新频道内的视频数据-api接口方式-获取50个视频更新
    """
    db_error_count = 0  # 数据库连接错误计数
    max_db_errors = 3   # 最大连续数据库错误次数

    while True:
        now = datetime.now()
        
        # 计算到下一个凌晨 0:10 的秒数
        target_time = now.replace(hour=1, minute=0, second=0, microsecond=0)
        if now.hour >= 0:
            target_time = target_time + timedelta(days=1)
        
        # 计算需要等待的秒数
        seconds_until_target = (target_time - now).total_seconds()
        
        logger.info(f"updateVideos_job：频道数据更新计划在 {target_time.strftime('%Y-%m-%d %H:%M:%S')} 运行，"
                  f"等待 {seconds_until_target:.2f} 秒")
        
        # 等待到目标时间
        await asyncio.sleep(seconds_until_target)
        
        # 运行统计数据更新
        try:
            logger.info("开始执行频道数据每日更新")
            youtube_main()
            logger.info(f"视频数据更新完成")

            # 成功执行，重置错误计数
            db_error_count = 0

        except Exception as e:
            error_msg = str(e)
            logger.error(f"视频数据更新出错: {error_msg}")

            # 检查是否是数据库连接池耗尽错误
            if "connection pool exhausted" in error_msg or "获取数据库连接失败" in error_msg:
                db_error_count += 1
                logger.error(f"数据库连接池耗尽，错误次数: {db_error_count}/{max_db_errors}")

                if db_error_count >= max_db_errors:
                    logger.critical(f"数据库连接池连续失败 {max_db_errors} 次，updateVideos_job 停止运行")
                    logger.critical("请检查数据库连接配置和连接池设置，或重启应用程序")
                    break  # 终止循环

        # 睡眠一小时，避免因时间计算误差导致短时间内重复执行
        await asyncio.sleep(3600)

    

async def start():
    """启动清理任务"""
    logger.info("启动更新频道的视频数据")
    asyncio.create_task(update_videos_job()) 