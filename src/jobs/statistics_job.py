import asyncio
from datetime import datetime, timedelta
from ..utils.channel_stats_updater import ChannelStatsUpdater
from ..utils.logger import get_logger

# 设置日志
logger = get_logger('statistics_job')

async def schedule_daily_statistics_update():
    """
    调度频道统计数据每日更新任务
    每天凌晨 2:00 运行，处理前一天的数据
    """
    db_error_count = 0  # 数据库连接错误计数
    max_db_errors = 3   # 最大连续数据库错误次数

    while True:
        now = datetime.now()
        
        # 计算到下一个凌晨 2:00 的秒数
        target_time = now.replace(hour=2, minute=0, second=0, microsecond=0)
        if now.hour >= 2:
            target_time = target_time + timedelta(days=1)
        
        # 计算需要等待的秒数
        seconds_until_target = (target_time - now).total_seconds()
        
        logger.info(f"statistics_job：频道统计数据更新计划在 {target_time.strftime('%Y-%m-%d %H:%M:%S')} 运行，"
                  f"等待 {seconds_until_target:.2f} 秒")
        
        # 等待到目标时间
        await asyncio.sleep(seconds_until_target)
        
        # 运行统计数据更新，支持数据修复
        try:
            logger.info("开始执行频道统计数据每日更新")

            # 首先检查数据完整性
            await check_and_repair_missing_data()

            # 处理前一天的数据
            target_date = datetime.now() - timedelta(days=1)
            success = ChannelStatsUpdater.update_all_channel_statistics(target_date)

            if success:
                logger.info("频道统计数据更新成功")
                # 成功执行，重置错误计数
                db_error_count = 0

                # 再次检查数据完整性
                await verify_statistics_data_integrity()
            else:
                logger.error("频道统计数据更新失败")
                await send_statistics_alert("统计数据更新失败", f"目标日期: {target_date.date()}")

        except Exception as e:
            error_msg = str(e)
            logger.error(f"频道统计数据更新出错: {error_msg}")

            # 检查是否是数据库连接池耗尽错误
            if "connection pool exhausted" in error_msg or "获取数据库连接失败" in error_msg:
                db_error_count += 1
                logger.error(f"数据库连接池耗尽，错误次数: {db_error_count}/{max_db_errors}")

                if db_error_count >= max_db_errors:
                    logger.critical(f"数据库连接池连续失败 {max_db_errors} 次，statistics_job 停止运行")
                    logger.critical("请检查数据库连接配置和连接池设置，或重启应用程序")
                    break  # 终止循环

        # 睡眠一小时，避免因时间计算误差导致短时间内重复执行
        await asyncio.sleep(3600)

async def check_and_repair_missing_data():
    """检查并修复缺失的数据"""
    try:
        from src.utils.channel_stats_updater import ChannelStatsUpdater

        logger.info("开始检查并修复缺失的数据")

        # 检查最近7天的数据完整性
        integrity_result = ChannelStatsUpdater.check_snapshot_data_integrity(days=7)

        if 'error' in integrity_result:
            logger.error(f"数据完整性检查失败: {integrity_result['error']}")
            return

        missing_statistics = integrity_result.get('missing_statistics', [])

        if missing_statistics:
            logger.warning(f"发现 {len(missing_statistics)} 条缺失的统计数据，尝试修复")

            # 按日期分组缺失的统计数据
            missing_by_date = {}
            for item in missing_statistics:
                date_str = item['missing_date']
                if date_str not in missing_by_date:
                    missing_by_date[date_str] = []
                missing_by_date[date_str].append(item)

            # 尝试修复每个缺失的日期
            repaired_count = 0
            for date_str, missing_items in missing_by_date.items():
                try:
                    target_date = datetime.strptime(date_str, '%Y-%m-%d')
                    logger.info(f"尝试修复 {date_str} 的统计数据，涉及 {len(missing_items)} 个频道")

                    success = ChannelStatsUpdater.update_all_channel_statistics(target_date)
                    if success:
                        repaired_count += len(missing_items)
                        logger.info(f"成功修复 {date_str} 的统计数据")
                    else:
                        logger.warning(f"修复 {date_str} 的统计数据失败")

                except Exception as e:
                    logger.error(f"修复 {date_str} 的统计数据时出错: {str(e)}")

            if repaired_count > 0:
                logger.info(f"数据修复完成，成功修复 {repaired_count} 条统计数据")
                await send_statistics_alert("数据修复成功", f"修复了 {repaired_count} 条统计数据")
            else:
                logger.warning("数据修复失败，没有成功修复任何数据")
                await send_statistics_alert("数据修复失败", "无法修复缺失的统计数据")
        else:
            logger.info("数据完整性检查通过，无需修复")

    except Exception as e:
        logger.error(f"数据检查和修复过程异常: {str(e)}")
        await send_statistics_alert("数据修复异常", str(e))

async def verify_statistics_data_integrity():
    """验证统计数据完整性"""
    try:
        from src.utils.channel_stats_updater import ChannelStatsUpdater

        # 检查最近3天的统计数据
        integrity_result = ChannelStatsUpdater.check_snapshot_data_integrity(days=3)

        if 'error' not in integrity_result:
            missing_statistics = integrity_result.get('missing_statistics', [])
            if missing_statistics:
                logger.warning(f"统计任务完成后仍有 {len(missing_statistics)} 条数据缺失")
                await send_statistics_alert("统计数据仍有缺失", f"缺失 {len(missing_statistics)} 条数据")
            else:
                logger.info("统计数据完整性验证通过")

    except Exception as e:
        logger.error(f"统计数据完整性验证异常: {str(e)}")

async def send_statistics_alert(alert_type: str, message: str):
    """发送统计任务告警"""
    try:
        from src.services.alert_service import send_warning_alert
        await send_warning_alert(alert_type, message, "统计数据更新任务")

    except Exception as e:
        logger.error(f"发送统计任务告警失败: {str(e)}")

async def start():
    """启动统计数据更新任务"""
    logger.info("启动频道统计数据每日更新任务")
    asyncio.create_task(schedule_daily_statistics_update())