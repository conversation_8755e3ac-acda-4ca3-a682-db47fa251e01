import asyncio
from datetime import datetime, timedelta
from ..utils.logger import get_logger
from ..dao.task_dao import TaskDao
from ..services.task_service import process_channel_videos_background

# 设置日志
logger = get_logger('task_scheduler')

async def task_scheduler():
    """
    后台任务调度器，定期检查并启动 pending 状态的任务。
    保证同一时间只有一个频道视频获取任务在运行。
    """
    logger.info("任务调度器已启动，开始检查待处理任务...")
    check_interval = 60 # 检查间隔为1分钟
    processing_check_interval = 10 # 处理完任务后检查间隔，10秒
    task_timeout = 300 # 任务超时时间，5分钟
    db_error_count = 0  # 数据库连接错误计数
    max_db_errors = 5   # 最大连续数据库错误次数

    while True:
        try:
            # 检查是否有任务在运行
            running_task = TaskDao.get_running_task()
            if running_task is None and db_error_count > 0:
                # 如果成功获取到数据，重置错误计数
                db_error_count = 0

            if running_task:
                # 检查任务是否超时
                last_updated = running_task.get('updated_at')
                if last_updated:
                    try:
                        last_updated_time = datetime.strptime(last_updated, '%Y-%m-%d %H:%M:%S')
                        time_diff = (datetime.now() - last_updated_time).total_seconds()
                        
                        if time_diff > task_timeout:
                            # 任务超时，标记为失败
                            error_msg = f"任务 {running_task['id']} 已超时（{time_diff}秒未更新），可能由于服务中断导致"
                            logger.warning(error_msg)
                            TaskDao.update_task_status(running_task['id'], "failed", error_message=error_msg)
                            await asyncio.sleep(processing_check_interval)
                            continue
                    except Exception as e:
                        logger.error(f"检查任务 {running_task['id']} 超时状态时出错: {str(e)}")
                
                logger.debug(f"调度器检测到任务 {running_task['id']} 正在运行，等待 {check_interval} 秒")
                await asyncio.sleep(check_interval)
                continue

            # 没有任务在运行，检查是否有待处理任务
            pending_task = TaskDao.get_next_pending_task()
            if pending_task is None and db_error_count > 0:
                # 如果成功获取到数据，重置错误计数
                db_error_count = 0

            if pending_task:
                task_id = pending_task['id']
                # 确保从数据库结果中正确获取 channel_id
                channel_id = pending_task.get('channel_id')
                
                if not channel_id:
                    logger.error(f"任务 {task_id} 缺少 channel_id，将标记为失败")
                    TaskDao.update_task_status(task_id, "failed", error_message="任务缺少 channel_id 参数")
                    await asyncio.sleep(processing_check_interval)
                    continue
                
                # 正确构建 channel_url
                channel_url = f"https://www.youtube.com/channel/{channel_id}"
                
                # 默认的最大视频数
                max_videos = 50
                
                # 尝试从任务参数中获取更详细的配置
                try:
                    # 根据 TaskDao 实际返回结构获取参数
                    if hasattr(pending_task, 'parameters'):
                        params = pending_task.get('parameters', {})
                    elif hasattr(pending_task, 'params'):
                        params = pending_task.get('params', {})
                    else:
                        params = {}
                        logger.warning(f"任务 {task_id} 缺少参数信息，使用默认配置")
                        
                    # 如果找到参数，则从中提取需要的值
                    if params:
                        # 可能的参数字段名
                        channel_url_candidates = ['channel_url', 'channelUrl', 'url']
                        for field in channel_url_candidates:
                            if field in params and params[field]:
                                channel_url = params[field]
                                break
                                
                        # 提取 max_videos
                        if 'max_videos' in params and params['max_videos']:
                            max_videos = int(params['max_videos'])
                except Exception as param_err:
                    logger.error(f"解析任务 {task_id} 参数时出错: {str(param_err)}")

                logger.info(f"调度器发现待处理任务 {task_id} (Channel: {channel_id})，准备启动...")
                TaskDao.update_task_status(task_id, "running")
                logger.info(f"任务 {task_id} 状态已更新为 running")

                # 在线程池中执行阻塞的后台处理函数
                loop = asyncio.get_running_loop()
                try:
                    logger.info(f"开始在线程池中执行任务 {task_id} 的处理函数...")
                    await loop.run_in_executor(
                        None, # 使用默认线程池
                        process_channel_videos_background,
                        task_id,
                        channel_id,
                        channel_url,
                        max_videos
                    )
                    logger.info(f"任务 {task_id} 的处理函数已在线程池中执行完毕。")

                except Exception as exec_error:
                    error_msg = f"执行任务 {task_id} 的后台函数时捕获到未处理异常: {str(exec_error)}"
                    logger.error(error_msg, exc_info=True)
                    try:
                        TaskDao.update_task_status(task_id, "failed", error_message=error_msg)
                    except Exception as db_err:
                        logger.error(f"尝试将失败任务 {task_id} 标记为失败时出错: {db_err}")

                # 任务执行完毕后，等待 10 秒再继续检查
                await asyncio.sleep(processing_check_interval)

            else:
                # 没有待处理任务
                logger.debug(f"调度器未发现待处理任务，等待 {check_interval} 秒")
                await asyncio.sleep(check_interval)

        except Exception as e:
            error_msg = str(e)
            logger.error(f"任务调度器主循环发生错误: {error_msg}", exc_info=True)

            # 检查是否是数据库连接池耗尽错误
            if "connection pool exhausted" in error_msg or "获取数据库连接失败" in error_msg:
                db_error_count += 1
                logger.error(f"数据库连接池耗尽，错误次数: {db_error_count}/{max_db_errors}")

                if db_error_count >= max_db_errors:
                    logger.critical(f"数据库连接池连续失败 {max_db_errors} 次，任务调度器停止运行")
                    logger.critical("请检查数据库连接配置和连接池设置，或重启应用程序")
                    break  # 终止循环

                # 数据库连接失败时等待更长时间
                await asyncio.sleep(check_interval * 2)
            else:
                # 其他异常时等待正常时间，防止频繁错误消耗资源
                await asyncio.sleep(check_interval)

async def start():
    """启动任务调度器"""
    logger.info("启动任务调度器")
    asyncio.create_task(task_scheduler()) 
    # TOOD-fwh-API 获取视频
    