import time
import random
import yt_dlp
from datetime import datetime
from typing import Dict, Any, Optional
from ..utils.logger import get_logger
from ..utils.download_utils import get_yt_dlp_opts
from ..utils.youtube_utils import format_duration
from ..dao.task_dao import TaskDao
from ..dao.youtube_dao import YouTubeDao
from .channel_service import get_channel_info

# 设置日志
logger = get_logger('task_service')

def process_channel_videos_background(task_id: str, channel_id: str, channel_url: str, max_videos: int):
    """在后台处理频道视频获取和保存的任务"""
    try:
        # 获取任务开始时间
        start_time = time.time()
        
        # 更新任务状态，记录开始时间
        try:
            TaskDao.update_task_status(task_id, "running", result={"started_at": start_time})
        except Exception as e:
            logger.warning(f"Task {task_id}: 更新任务开始时间失败: {str(e)}")
        
        # 获取频道信息
        try:
            channel_info_data = get_channel_info(channel_id)
            channel_title = channel_info_data["snippet"]["title"]
        except Exception as e:
            error_msg = f"获取频道信息时发生错误: {str(e)}"
            logger.error(f"Task {task_id}: {error_msg}")
            TaskDao.update_task_status(task_id, "failed", error_message=error_msg)
            return
            
        # 设置yt-dlp选项
        ydl_opts = get_yt_dlp_opts({
            'extract_flat': True,  # 获取播放列表而不下载视频
        })
        
        # 使用 yt-dlp 获取视频列表
        try:
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                # 确保使用有效的 URL
                if not channel_url or '{channel_id}' in channel_url:
                    channel_url = f"https://www.youtube.com/channel/{channel_id}/videos"
                
                info = ydl.extract_info(channel_url, download=False)
                videos = info.get('entries', [])
            logger.info(f"Task {task_id}: 从频道 {channel_title} 获取到 {len(videos)} 个视频列表")
        except Exception as e:
            error_msg = f"使用yt-dlp获取视频列表失败: {str(e)}"
            logger.error(f"Task {task_id}: {error_msg}")
            TaskDao.update_task_status(task_id, "failed", error_message=error_msg)
            return

        # 开始获取每个视频的详细信息
        processed_count = 0
        saved_count = 0
        skipped_count = 0
        last_status_update = time.time()

        # 设置视频获取选项
        video_opts = get_yt_dlp_opts({
            'skip_download': True,  # 不下载视频
        })
        
        # 批量处理视频 - 每批次最多处理的视频数量
        batch_size = 10
        batch_videos = []
        
        for video_entry in videos:
            video_id = video_entry['id']
            
            # 检查视频是否已存在
            existing_video = YouTubeDao.get_video_by_youtube_id(str(video_id))
            if existing_video:
                logger.info(f"Task {task_id}: 视频已存在于数据库: {video_id}, 跳过。")
                skipped_count += 1
                continue

            processed_count += 1
            
            try:
                # 添加随机延迟
                delay = random.uniform(1, 3)
                time.sleep(delay)
                
                with yt_dlp.YoutubeDL(video_opts) as ydl:
                    video_info = ydl.extract_info(f"https://www.youtube.com/watch?v={video_id}", download=False)
                
                # 构建保存到数据库的视频信息
                video_data = {
                    'id': video_id,
                    'channelId': video_info.get('channel_id'),
                    'title': video_info.get('title', ''),
                    'description': video_info.get('description', ''),
                    'publishedAt': video_info.get('upload_date', None),
                    'thumbnailUrl': video_info.get('thumbnail', ''),
                    'viewCount': int(video_info.get('view_count', 0) or 0),
                    'likeCount': int(video_info.get('like_count', 0) or 0),
                    'commentCount': int(video_info.get('comment_count', 0) or 0),
                    'duration': format_duration(video_info.get('duration', 0)),
                    'tags': video_info.get('tags', []),
                    'categories': video_info.get('categories', []),
                    'uploadDate': video_info.get('upload_date'),
                    'channel': video_info.get('channel', ''),
                    'timestamp': video_info.get('timestamp'),
                    'thumbnails': video_info.get('thumbnails', []),
                    'raw_response': video_info
                }

                # publishedAt 格式转换
                if video_data['publishedAt']:
                    try:
                        dt_object = datetime.strptime(video_data['publishedAt'], '%Y%m%d')
                        video_data['publishedAt'] = dt_object.strftime('%Y-%m-%d %H:%M:%S') 
                    except ValueError:
                        logger.warning(f"Task {task_id}: 视频 {video_id} 的日期格式无效: {video_data['publishedAt']}")
                        video_data['publishedAt'] = None

                # 添加到批次处理列表
                batch_videos.append(video_data)
                
                # 达到批次大小或最后一个视频时，进行批量保存
                if len(batch_videos) >= batch_size or video_id == videos[-1]['id']:
                    try:
                        # 实现批量保存逻辑（如果 YouTubeDao 支持）
                        for video in batch_videos:
                            # 目前仍使用单个视频保存
                            if YouTubeDao.save_video(video):
                                saved_count += 1
                                logger.info(f"Task {task_id}: 已保存视频: {video['title']} (ID: {video['id']})")
                            else:
                                logger.error(f"Task {task_id}: 保存视频失败: {video['title']} (ID: {video['id']})")
                    except Exception as batch_error:
                        logger.error(f"Task {task_id}: 批量处理视频时出错: {str(batch_error)}")
                    
                    # 清空批次
                    batch_videos = []
                
                # 定期更新任务状态（每分钟更新一次）
                current_time = time.time()
                if current_time - last_status_update >= 60:
                    progress = {
                        "processed": processed_count,
                        "saved": saved_count,
                        "skipped": skipped_count,
                        "total_videos": len(videos),
                        "last_updated": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    }
                    TaskDao.update_task_status(task_id, "running", result=progress)
                    last_status_update = current_time
                
            except Exception as e:
                error_msg = str(e)
                if "members-only content" in error_msg or "Join this channel" in error_msg:
                    logger.warning(f"Task {task_id}: 跳过会员专属视频: {video_id}, 原因: {error_msg}")
                elif "Private video" in error_msg:
                     logger.warning(f"Task {task_id}: 跳过私密视频: {video_id}, 原因: {error_msg}")
                elif "Video unavailable" in error_msg:
                     logger.warning(f"Task {task_id}: 跳过不可用视频: {video_id}, 原因: {error_msg}")
                else:
                    logger.error(f"Task {task_id}: 处理视频 {video_id} 失败: {error_msg}")
                continue
        
        # 计算总处理时间
        processing_time = time.time() - start_time
        processing_minutes = round(processing_time / 60, 2)
        
        # 所有视频处理完毕，更新任务状态为成功
        TaskDao.update_task_status(task_id, "success", result={
            "processed_count": processed_count,
            "saved_count": saved_count,
            "skipped_count": skipped_count,
            "total_videos_listed": len(videos),
            "channel_title": channel_title,
            "processing_time_minutes": processing_minutes,
            "last_updated": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        })
        logger.info(f"Task {task_id}: 频道 {channel_title} 视频处理完成。用时: {processing_minutes}分钟, 处理: {processed_count}, 保存: {saved_count}, 跳过: {skipped_count}, 列表总数: {len(videos)}")

    except Exception as e:
        # 捕获后台任务中的顶层异常
        error_msg = f"后台任务执行失败: {str(e)}"
        logger.error(f"Task {task_id}: {error_msg}", exc_info=True)
        try:
            TaskDao.update_task_status(task_id, "failed", error_message=error_msg)
        except Exception as db_error:
            logger.error(f"Task {task_id}: 更新任务状态为失败时也发生错误: {str(db_error)}")


def get_channel_videos_by_date_range(channel_id: str, start_date: str, end_date: str, max_videos: int = 50) -> Dict[str, Any]:
    """获取指定日期范围内的频道视频"""
    # 创建任务记录
    task_params = {
        "channel_id": channel_id, 
        "start_date": start_date,
        "end_date": end_date,
        "fetch_type": "date_range_videos"
    }
    task_id = TaskDao.create_task("date_range_videos", task_params)
    
    # 更新任务状态为运行中
    TaskDao.update_task_status(task_id, "running")
    
    # 获取频道信息
    channel_info = get_channel_info(channel_id)
    channel_title = channel_info["snippet"]["title"]
    
    # 设置yt-dlp选项
    ydl_opts = get_yt_dlp_opts({
        'extract_flat': True,  # 获取播放列表而不下载视频
    })
    
    channel_url = f"https://www.youtube.com/channel/{channel_id}/videos"
    
    try:
        # 获取频道所有视频
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(channel_url, download=False)
            videos = info.get('entries', [])
        
        logger.info(f"从频道 {channel_title} 获取到 {len(videos)} 个视频")
        
        # 开始获取每个视频的详细信息并根据日期过滤
        filtered_videos = []
        processed_count = 0
        saved_count = 0
        updated_count = 0
        
        # 转换日期字符串为datetime对象，用于比较
        start_date_obj = datetime.strptime(start_date, "%Y%m%d")
        end_date_obj = datetime.strptime(end_date, "%Y%m%d")
        
        for video_entry in videos:
            if processed_count >= max_videos:
                break
            
            video_id = video_entry['id']
            processed_count += 1
            
            # 获取视频详细信息
            video_opts = get_yt_dlp_opts({
                'skip_download': True,  # 不下载视频
            })
            
            try:
                with yt_dlp.YoutubeDL(video_opts) as ydl:
                    video_info = ydl.extract_info(f"https://www.youtube.com/watch?v={video_id}", download=False)
                    
                # 提取上传日期
                upload_date_str = video_info.get('upload_date')
                if not upload_date_str:
                    continue
                
                # 转换上传日期为datetime对象
                upload_date = datetime.strptime(upload_date_str, "%Y%m%d")
                
                # 检查是否在日期范围内
                if start_date_obj <= upload_date <= end_date_obj:
                    # 构建保存到数据库的视频信息
                    video_data = {
                        'id': video_id,
                        'channelId': video_info.get('channel_id'),
                        'title': video_info.get('title', ''),
                        'description': video_info.get('description', ''),
                        'publishedAt': upload_date.strftime("%Y-%m-%d %H:%M:%S"),
                        'thumbnailUrl': video_info.get('thumbnail', ''),
                        'viewCount': int(video_info.get('view_count', 0) or 0),
                        'likeCount': int(video_info.get('like_count', 0) or 0),
                        'commentCount': int(video_info.get('comment_count', 0) or 0),
                        'duration': format_duration(video_info.get('duration', 0)),
                        'tags': video_info.get('tags', []),
                        'categories': video_info.get('categories', []),
                        'uploadDate': video_info.get('upload_date'),
                        'channel': video_info.get('channel', ''),
                        'timestamp': video_info.get('timestamp'),
                        'thumbnails': video_info.get('thumbnails', []),
                        'raw_response': video_info
                    }
                    
                    # 检查视频是否已存在
                    existing_video = YouTubeDao.get_video_by_youtube_id(str(video_id))
                    
                    if existing_video and len(existing_video) > 0:
                        # 检查数据是否有变化
                        if (existing_video[0][1] != video_data['viewCount'] or 
                            existing_video[0][2] != video_data['likeCount'] or 
                            existing_video[0][3] != video_data['commentCount']):
                            
                            # 更新视频数据
                            YouTubeDao.update_video(video_data)
                            updated_count += 1
                            logger.info(f"已更新视频: {video_data['title']}, 观看次数从 {existing_video[0][1]} 更新为 {video_data['viewCount']}")
                        else:
                            logger.info(f"视频数据无变化，跳过更新: {video_data['title']}")
                    else:
                        # 保存新视频到数据库
                        if YouTubeDao.save_video(video_data):
                            saved_count += 1
                            # logger.info(f"已保存新视频: {video_data['title']}")
                        else:
                            logger.error(f"保存视频失败: {video_data['title']}")
                    
                    filtered_videos.append(video_data)
                    
            except Exception as e:
                error_msg = str(e)
                # 会员专属视频，记录错误并跳过
                if "members-only content" in error_msg or "Join this channel" in error_msg:
                    logger.warning(f"跳过会员专属视频: {video_id}, 原因: {error_msg}")
                else:
                    logger.error(f"处理视频 {video_id} 失败: {error_msg}")
                continue
        
        # 更新任务状态为成功
        TaskDao.update_task_status(task_id, "success", result={
            "processed_count": processed_count,
            "saved_count": saved_count,
            "updated_count": updated_count,
            "channel_title": channel_title
        })
        
        return {
            "message": f"成功获取频道 {channel_title} 在指定日期范围内的视频",
            "channel_id": channel_id,
            "channel_title": channel_title,
            "date_range": f"{start_date} 至 {end_date}",
            "videos_found": len(filtered_videos),
            "videos_saved": saved_count,
            "videos_updated": updated_count,
            "videos_processed": processed_count,
            "task_id": task_id
        }
        
    except Exception as e:
        error_msg = f"获取频道视频失败: {str(e)}"
        logger.error(error_msg)
        TaskDao.update_task_status(task_id, "failed", error_message=error_msg)
        raise e 