import os
import requests
from typing import List, Dict, Any, Optional
from fastapi import HTTPException
from ..utils.logger import get_logger

# 从环境变量获取YouTube API密钥
YOUTUBE_API_KEY = os.getenv("YOUTUBE_API_KEY", "")

# 设置日志
logger = get_logger('video_category_service')

def get_channel_video_ids(channel_id: str, max_results: int = 5) -> List[str]:
    """
    获取频道下的视频ID列表
    
    Args:
        channel_id (str): YouTube频道ID
        max_results (int): 最大获取视频数量，默认5
        
    Returns:
        List[str]: 视频ID列表
    """
    try:
        url = "https://www.googleapis.com/youtube/v3/search"
        params = {
            "key": YOUTUBE_API_KEY,
            "channelId": channel_id,
            "part": "id",
            "maxResults": max_results,
            "type": "video",
            "order": "date"  # 按日期排序，获取最新视频
        }
        
        response = requests.get(url, params=params)
        if response.status_code != 200:
            logger.error(f"获取频道视频列表失败: {response.text}")
            raise HTTPException(
                status_code=response.status_code, 
                detail=f"YouTube API请求失败: {response.text}"
            )
        
        data = response.json()
        video_ids = [item['id']['videoId'] for item in data.get('items', [])]
        
        logger.info(f"成功获取频道 {channel_id} 的 {len(video_ids)} 个视频ID")
        return video_ids
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取频道视频ID时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取频道视频ID失败: {str(e)}")

def get_video_categories(video_ids: List[str]) -> List[Dict[str, str]]:
    """
    获取视频的分类ID
    
    Args:
        video_ids (List[str]): 视频ID列表
        
    Returns:
        List[Dict[str, str]]: 包含视频ID和分类ID的字典列表
    """
    if not video_ids:
        return []
        
    try:
        url = "https://www.googleapis.com/youtube/v3/videos"
        params = {
            "key": YOUTUBE_API_KEY,
            "id": ",".join(video_ids),
            "part": "snippet"
        }
        
        response = requests.get(url, params=params)
        if response.status_code != 200:
            logger.error(f"获取视频分类信息失败: {response.text}")
            raise HTTPException(
                status_code=response.status_code, 
                detail=f"YouTube API请求失败: {response.text}"
            )
        
        data = response.json()
        video_categories = []
        
        for item in data.get('items', []):
            video_id = item['id']
            category_id = item['snippet'].get('categoryId', '0')  # 默认分类ID为0
            video_categories.append({
                'video_id': video_id,
                'category_id': category_id
            })
        
        logger.info(f"成功获取 {len(video_categories)} 个视频的分类信息")
        return video_categories
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取视频分类信息时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取视频分类信息失败: {str(e)}")

def get_category_titles(category_ids: List[str], region_code: str = 'US') -> Dict[str, str]:
    """
    获取分类ID对应的分类名称
    
    Args:
        category_ids (List[str]): 分类ID列表
        region_code (str): 地区代码，默认US
        
    Returns:
        Dict[str, str]: 分类ID到分类名称的映射字典
    """
    if not category_ids:
        return {}
        
    try:
        # 去重分类ID
        unique_category_ids = list(set(category_ids))
        
        url = "https://www.googleapis.com/youtube/v3/videoCategories"
        params = {
            "key": YOUTUBE_API_KEY,
            "id": ",".join(unique_category_ids),
            "part": "snippet",
            "regionCode": region_code
        }
        
        response = requests.get(url, params=params)
        if response.status_code != 200:
            logger.error(f"获取视频分类标题失败: {response.text}")
            raise HTTPException(
                status_code=response.status_code, 
                detail=f"YouTube API请求失败: {response.text}"
            )
        
        data = response.json()
        category_titles = {}
        
        for item in data.get('items', []):
            category_id = item['id']
            category_title = item['snippet']['title']
            category_titles[category_id] = category_title
        
        logger.info(f"成功获取 {len(category_titles)} 个分类标题")
        return category_titles
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取分类标题时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取分类标题失败: {str(e)}")

def get_channel_video_categories(channel_id: str, max_results: int = 5, region_code: str = 'US') -> Dict[str, Any]:
    """
    获取频道视频的分类信息（主要功能函数）
    
    Args:
        channel_id (str): YouTube频道ID
        max_results (int): 最大获取视频数量，默认5
        region_code (str): 地区代码，默认US
        
    Returns:
        Dict[str, Any]: 包含频道ID、视频分类列表和总数的字典
    """
    try:
        logger.info(f"开始获取频道 {channel_id} 的视频分类信息")
        
        # 1. 获取频道下的视频ID
        video_ids = get_channel_video_ids(channel_id, max_results)
        
        if not video_ids:
            logger.warning(f"频道 {channel_id} 没有找到视频")
            return {
                "channel_id": channel_id,
                "videos": [],
                "total_count": 0
            }
        
        # 2. 获取视频的分类ID
        video_categories = get_video_categories(video_ids)
        
        # 3. 获取分类ID对应的分类名称
        category_ids = [vc['category_id'] for vc in video_categories]
        category_titles = get_category_titles(category_ids, region_code)
        
        # 4. 组合结果
        result_videos = []
        for vc in video_categories:
            video_id = vc['video_id']
            category_id = vc['category_id']
            category_title = category_titles.get(category_id, "Unknown")
            
            result_videos.append({
                "video_id": video_id,
                "category_id": category_id,
                "category_title": category_title
            })
            
            logger.info(f"视频 {video_id}: 分类ID {category_id}, 分类名称: {category_title}")
        
        result = {
            "channel_id": channel_id,
            "videos": result_videos,
            "total_count": len(result_videos)
        }
        
        logger.info(f"成功获取频道 {channel_id} 的 {len(result_videos)} 个视频分类信息")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取频道视频分类信息时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取频道视频分类信息失败: {str(e)}")
