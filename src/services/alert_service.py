#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
告警通知服务
支持多种通知方式：邮件、Slack、钉钉、企业微信等
"""

import os
import json
import asyncio
from datetime import datetime
from typing import Dict, List, Optional
from enum import Enum

from src.utils.logger import get_logger

logger = get_logger('alert_service')

class AlertLevel(Enum):
    """告警级别"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class AlertService:
    """告警服务类"""
    
    def __init__(self):
        self.enabled = os.getenv('ALERT_ENABLED', 'false').lower() == 'true'
        self.email_enabled = os.getenv('EMAIL_ALERT_ENABLED', 'false').lower() == 'true'
        self.slack_enabled = os.getenv('SLACK_ALERT_ENABLED', 'false').lower() == 'true'
        self.dingtalk_enabled = os.getenv('DINGTALK_ALERT_ENABLED', 'false').lower() == 'true'
        
        # 配置信息
        self.email_config = {
            'smtp_server': os.getenv('SMTP_SERVER'),
            'smtp_port': int(os.getenv('SMTP_PORT', '587')),
            'username': os.getenv('SMTP_USERNAME'),
            'password': os.getenv('SMTP_PASSWORD'),
            'from_email': os.getenv('ALERT_FROM_EMAIL'),
            'to_emails': os.getenv('ALERT_TO_EMAILS', '').split(',') if os.getenv('ALERT_TO_EMAILS') else []
        }
        
        self.slack_config = {
            'webhook_url': os.getenv('SLACK_WEBHOOK_URL'),
            'channel': os.getenv('SLACK_CHANNEL', '#alerts')
        }
        
        self.dingtalk_config = {
            'webhook_url': os.getenv('DINGTALK_WEBHOOK_URL'),
            'secret': os.getenv('DINGTALK_SECRET')
        }
    
    async def send_alert(self, 
                        title: str, 
                        message: str, 
                        level: AlertLevel = AlertLevel.WARNING,
                        component: str = "系统",
                        details: Optional[Dict] = None):
        """发送告警通知
        
        Args:
            title: 告警标题
            message: 告警消息
            level: 告警级别
            component: 组件名称
            details: 详细信息
        """
        if not self.enabled:
            logger.info(f"告警功能未启用，跳过告警: {title}")
            return
        
        alert_data = {
            'title': title,
            'message': message,
            'level': level.value,
            'component': component,
            'timestamp': datetime.now().isoformat(),
            'details': details or {}
        }
        
        logger.info(f"发送告警: [{level.value.upper()}] {component} - {title}")
        
        # 并发发送到各个通知渠道
        tasks = []
        
        if self.email_enabled and self._is_email_configured():
            tasks.append(self._send_email_alert(alert_data))
        
        if self.slack_enabled and self._is_slack_configured():
            tasks.append(self._send_slack_alert(alert_data))
        
        if self.dingtalk_enabled and self._is_dingtalk_configured():
            tasks.append(self._send_dingtalk_alert(alert_data))
        
        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 检查发送结果
            success_count = 0
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"告警发送失败 (渠道 {i+1}): {str(result)}")
                else:
                    success_count += 1
            
            logger.info(f"告警发送完成: {success_count}/{len(tasks)} 个渠道成功")
        else:
            logger.warning("没有配置可用的告警通知渠道")
    
    async def _send_email_alert(self, alert_data: Dict):
        """发送邮件告警"""
        try:
            import smtplib
            from email.mime.text import MIMEText
            from email.mime.multipart import MIMEMultipart
            
            # 构建邮件内容
            subject = f"[{alert_data['level'].upper()}] {alert_data['component']} - {alert_data['title']}"
            
            body = f"""
告警时间: {alert_data['timestamp']}
组件: {alert_data['component']}
级别: {alert_data['level'].upper()}
标题: {alert_data['title']}
消息: {alert_data['message']}

详细信息:
{json.dumps(alert_data['details'], indent=2, ensure_ascii=False)}
            """.strip()
            
            # 创建邮件
            msg = MIMEMultipart()
            msg['From'] = self.email_config['from_email']
            msg['To'] = ', '.join(self.email_config['to_emails'])
            msg['Subject'] = subject
            
            msg.attach(MIMEText(body, 'plain', 'utf-8'))
            
            # 发送邮件
            with smtplib.SMTP(self.email_config['smtp_server'], self.email_config['smtp_port']) as server:
                server.starttls()
                server.login(self.email_config['username'], self.email_config['password'])
                server.send_message(msg)
            
            logger.info("邮件告警发送成功")
            
        except Exception as e:
            logger.error(f"邮件告警发送失败: {str(e)}")
            raise
    
    async def _send_slack_alert(self, alert_data: Dict):
        """发送Slack告警"""
        try:
            import aiohttp
            
            # 根据告警级别选择颜色
            color_map = {
                'info': '#36a64f',      # 绿色
                'warning': '#ff9500',   # 橙色
                'error': '#ff0000',     # 红色
                'critical': '#8b0000'   # 深红色
            }
            
            payload = {
                'channel': self.slack_config['channel'],
                'username': 'AlertBot',
                'icon_emoji': ':warning:',
                'attachments': [{
                    'color': color_map.get(alert_data['level'], '#ff9500'),
                    'title': f"{alert_data['component']} - {alert_data['title']}",
                    'text': alert_data['message'],
                    'fields': [
                        {
                            'title': '告警级别',
                            'value': alert_data['level'].upper(),
                            'short': True
                        },
                        {
                            'title': '时间',
                            'value': alert_data['timestamp'],
                            'short': True
                        }
                    ],
                    'footer': 'YouTube数据采集系统',
                    'ts': int(datetime.now().timestamp())
                }]
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(self.slack_config['webhook_url'], json=payload) as response:
                    if response.status == 200:
                        logger.info("Slack告警发送成功")
                    else:
                        raise Exception(f"Slack API返回错误: {response.status}")
                        
        except Exception as e:
            logger.error(f"Slack告警发送失败: {str(e)}")
            raise
    
    async def _send_dingtalk_alert(self, alert_data: Dict):
        """发送钉钉告警"""
        try:
            import aiohttp
            import hmac
            import hashlib
            import base64
            import urllib.parse
            
            # 如果配置了签名，计算签名
            timestamp = str(round(datetime.now().timestamp() * 1000))
            webhook_url = self.dingtalk_config['webhook_url']
            
            if self.dingtalk_config['secret']:
                secret = self.dingtalk_config['secret']
                string_to_sign = f"{timestamp}\n{secret}"
                hmac_code = hmac.new(
                    secret.encode('utf-8'),
                    string_to_sign.encode('utf-8'),
                    digestmod=hashlib.sha256
                ).digest()
                sign = urllib.parse.quote_plus(base64.b64encode(hmac_code))
                webhook_url = f"{webhook_url}&timestamp={timestamp}&sign={sign}"
            
            # 构建消息内容
            level_emoji = {
                'info': '✅',
                'warning': '⚠️',
                'error': '❌',
                'critical': '🚨'
            }
            
            text = f"""
{level_emoji.get(alert_data['level'], '⚠️')} **{alert_data['component']}告警**

**标题**: {alert_data['title']}
**级别**: {alert_data['level'].upper()}
**时间**: {alert_data['timestamp']}
**消息**: {alert_data['message']}
            """.strip()
            
            payload = {
                'msgtype': 'markdown',
                'markdown': {
                    'title': f"{alert_data['component']}告警",
                    'text': text
                }
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(webhook_url, json=payload) as response:
                    if response.status == 200:
                        result = await response.json()
                        if result.get('errcode') == 0:
                            logger.info("钉钉告警发送成功")
                        else:
                            raise Exception(f"钉钉API返回错误: {result}")
                    else:
                        raise Exception(f"钉钉API返回错误: {response.status}")
                        
        except Exception as e:
            logger.error(f"钉钉告警发送失败: {str(e)}")
            raise
    
    def _is_email_configured(self) -> bool:
        """检查邮件配置是否完整"""
        return all([
            self.email_config['smtp_server'],
            self.email_config['username'],
            self.email_config['password'],
            self.email_config['from_email'],
            self.email_config['to_emails']
        ])
    
    def _is_slack_configured(self) -> bool:
        """检查Slack配置是否完整"""
        return bool(self.slack_config['webhook_url'])
    
    def _is_dingtalk_configured(self) -> bool:
        """检查钉钉配置是否完整"""
        return bool(self.dingtalk_config['webhook_url'])

# 全局告警服务实例
alert_service = AlertService()

# 便捷函数
async def send_info_alert(title: str, message: str, component: str = "系统", details: Dict = None):
    """发送信息级别告警"""
    await alert_service.send_alert(title, message, AlertLevel.INFO, component, details)

async def send_warning_alert(title: str, message: str, component: str = "系统", details: Dict = None):
    """发送警告级别告警"""
    await alert_service.send_alert(title, message, AlertLevel.WARNING, component, details)

async def send_error_alert(title: str, message: str, component: str = "系统", details: Dict = None):
    """发送错误级别告警"""
    await alert_service.send_alert(title, message, AlertLevel.ERROR, component, details)

async def send_critical_alert(title: str, message: str, component: str = "系统", details: Dict = None):
    """发送严重级别告警"""
    await alert_service.send_alert(title, message, AlertLevel.CRITICAL, component, details)
