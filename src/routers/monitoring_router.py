#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
监控和管理API路由
提供任务健康检查、数据完整性检查、手动触发任务等功能
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
from datetime import datetime, timedelta
from typing import Optional
import asyncio

from src.utils.logger import get_logger
from src.utils.channel_stats_updater import ChannelStatsUpdater
from src.dao.task_dao import TaskDao
from src.services.channel_service import update_channel_info_snapshot

logger = get_logger('monitoring_router')

router = APIRouter(prefix="/monitoring", tags=["监控管理"])

@router.get("/health/jobs")
async def jobs_health_check():
    """获取所有定时任务的健康状态"""
    try:
        # 检查任务状态
        pending_tasks = TaskDao.count_pending_tasks()
        running_tasks = TaskDao.count_running_tasks()
        
        # 检查最近的任务执行情况
        recent_tasks = TaskDao.get_recent_tasks(hours=24)
        
        health_status = {
            "timestamp": datetime.now().isoformat(),
            "overall_status": "healthy",
            "tasks": {
                "task_scheduler": {
                    "status": "running",
                    "pending_tasks": pending_tasks,
                    "running_tasks": running_tasks
                },
                "recent_executions": len(recent_tasks)
            },
            "next_scheduled_times": {
                "channel_update": get_next_execution_time(0, 10),  # 凌晨0:10
                "videos_update": get_next_execution_time(1, 0),    # 凌晨1:00
                "statistics": get_next_execution_time(2, 0),       # 凌晨2:00
                "channel_all_update": get_next_execution_time(3, 0) # 凌晨3:00
            }
        }
        
        # 检查是否有异常情况
        if running_tasks > 5:  # 如果运行中的任务过多
            health_status["overall_status"] = "warning"
            health_status["warnings"] = ["运行中任务数量过多"]
        
        return health_status
        
    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        return {
            "timestamp": datetime.now().isoformat(),
            "overall_status": "unhealthy",
            "error": str(e)
        }

@router.get("/data-integrity")
async def check_data_integrity(days: int = 7):
    """检查数据完整性"""
    try:
        logger.info(f"开始检查最近 {days} 天的数据完整性")
        
        integrity_result = ChannelStatsUpdater.check_snapshot_data_integrity(days=days)
        
        if 'error' in integrity_result:
            raise HTTPException(status_code=500, detail=f"数据完整性检查失败: {integrity_result['error']}")
        
        missing_snapshots = integrity_result.get('missing_snapshots', [])
        missing_statistics = integrity_result.get('missing_statistics', [])
        
        # 计算数据完整性得分
        total_expected = integrity_result.get('total_channels', 1) * days
        missing_total = len(missing_snapshots) + len(missing_statistics)
        integrity_score = max(0, (total_expected - missing_total) / total_expected * 100)
        
        return {
            "timestamp": datetime.now().isoformat(),
            "check_period": integrity_result.get('check_period'),
            "integrity_score": round(integrity_score, 2),
            "status": "healthy" if integrity_score >= 95 else "warning" if integrity_score >= 80 else "critical",
            "summary": {
                "total_channels": integrity_result.get('total_channels', 0),
                "missing_snapshots": len(missing_snapshots),
                "missing_statistics": len(missing_statistics),
                "total_missing": missing_total
            },
            "details": {
                "missing_snapshots": missing_snapshots[:10],  # 只返回前10条
                "missing_statistics": missing_statistics[:10]  # 只返回前10条
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"数据完整性检查异常: {str(e)}")
        raise HTTPException(status_code=500, detail=f"数据完整性检查异常: {str(e)}")

@router.post("/repair/missing-data")
async def repair_missing_data(background_tasks: BackgroundTasks, days: int = 7):
    """修复缺失的数据"""
    try:
        logger.info(f"开始修复最近 {days} 天的缺失数据")

        # 在后台执行数据修复
        background_tasks.add_task(perform_data_repair, days)

        return {
            "message": f"数据修复任务已提交，将修复最近 {days} 天的缺失数据",
            "status": "submitted",
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"提交数据修复任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"提交数据修复任务失败: {str(e)}")

@router.post("/repair/channel/{channel_id}")
async def repair_channel_data(
    channel_id: str,
    background_tasks: BackgroundTasks,
    days: int = 7,
    repair_snapshots: bool = True,
    repair_statistics: bool = True,
    force_repair: bool = False
):
    """修复指定频道的缺失数据

    Args:
        channel_id: 频道ID
        days: 检查最近多少天的数据 (默认7天)
        repair_snapshots: 是否修复快照数据 (默认True)
        repair_statistics: 是否修复统计数据 (默认True)
        force_repair: 是否强制修复 (默认False) - 启用时会尝试更激进的修复策略
    """
    try:
        logger.info(f"开始修复频道 {channel_id} 最近 {days} 天的缺失数据")

        # 验证频道是否存在
        from src.dao.channel_dao import ChannelDao
        channel_info = ChannelDao.get_channel_by_id(channel_id)
        if not channel_info:
            raise HTTPException(status_code=404, detail=f"频道 {channel_id} 不存在")

        # 在后台执行指定频道的数据修复
        background_tasks.add_task(
            perform_channel_data_repair,
            channel_id,
            days,
            repair_snapshots,
            repair_statistics,
            force_repair
        )

        return {
            "message": f"频道 {channel_id} 数据修复任务已提交",
            "channel_id": channel_id,
            "channel_name": channel_info.get('channel_name', 'Unknown'),
            "repair_config": {
                "days": days,
                "repair_snapshots": repair_snapshots,
                "repair_statistics": repair_statistics,
                "force_repair": force_repair
            },
            "status": "submitted",
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"提交频道数据修复任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"提交频道数据修复任务失败: {str(e)}")

@router.get("/repair/channel/{channel_id}/check")
async def check_channel_data_integrity(channel_id: str, days: int = 7):
    """检查指定频道的数据完整性"""
    try:
        logger.info(f"检查频道 {channel_id} 最近 {days} 天的数据完整性")

        # 验证频道是否存在
        from src.dao.channel_dao import ChannelDao
        channel_info = ChannelDao.get_channel_by_id(channel_id)
        if not channel_info:
            raise HTTPException(status_code=404, detail=f"频道 {channel_id} 不存在")

        # 检查频道数据完整性
        integrity_result = ChannelStatsUpdater.check_channel_data_integrity(channel_id, days)

        if 'error' in integrity_result:
            raise HTTPException(status_code=500, detail=f"数据完整性检查失败: {integrity_result['error']}")

        missing_snapshots = integrity_result.get('missing_snapshots', [])
        missing_statistics = integrity_result.get('missing_statistics', [])

        # 计算数据完整性得分
        expected_snapshots = days
        expected_statistics = days
        actual_snapshots = expected_snapshots - len(missing_snapshots)
        actual_statistics = expected_statistics - len(missing_statistics)

        snapshot_score = (actual_snapshots / expected_snapshots * 100) if expected_snapshots > 0 else 100
        statistics_score = (actual_statistics / expected_statistics * 100) if expected_statistics > 0 else 100
        overall_score = (snapshot_score + statistics_score) / 2

        return {
            "timestamp": datetime.now().isoformat(),
            "channel_id": channel_id,
            "channel_name": channel_info.get('channel_name', 'Unknown'),
            "check_period": integrity_result.get('check_period'),
            "overall_integrity_score": round(overall_score, 2),
            "status": "healthy" if overall_score >= 95 else "warning" if overall_score >= 80 else "critical",
            "details": {
                "snapshots": {
                    "expected": expected_snapshots,
                    "actual": actual_snapshots,
                    "missing": len(missing_snapshots),
                    "score": round(snapshot_score, 2),
                    "missing_dates": [item['missing_date'] for item in missing_snapshots]
                },
                "statistics": {
                    "expected": expected_statistics,
                    "actual": actual_statistics,
                    "missing": len(missing_statistics),
                    "score": round(statistics_score, 2),
                    "missing_dates": [item['missing_date'] for item in missing_statistics]
                }
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"检查频道数据完整性异常: {str(e)}")
        raise HTTPException(status_code=500, detail=f"检查频道数据完整性异常: {str(e)}")

@router.post("/repair/channel/{channel_id}/emergency")
async def emergency_repair_channel_data(
    channel_id: str,
    background_tasks: BackgroundTasks,
    days: int = 7,
    use_zero_growth: bool = True,
    use_estimation: bool = True
):
    """紧急修复指定频道数据 - 用于严重数据缺失的情况

    当常规修复方法失败时使用此接口，会采用更激进的修复策略：
    1. 零增长假设：假设数据没有变化，使用最近可用数据填充
    2. 数据估算：基于历史趋势进行数据估算
    3. 占位符数据：创建标记为估算的占位符数据

    Args:
        channel_id: 频道ID
        days: 修复最近多少天的数据
        use_zero_growth: 是否使用零增长假设
        use_estimation: 是否使用数据估算
    """
    try:
        logger.info(f"紧急修复频道 {channel_id} 最近 {days} 天的数据")

        # 验证频道是否存在
        from src.dao.channel_dao import ChannelDao
        channel_info = ChannelDao.get_channel_by_id(channel_id)
        if not channel_info:
            raise HTTPException(status_code=404, detail=f"频道 {channel_id} 不存在")

        # 在后台执行紧急修复
        background_tasks.add_task(
            perform_emergency_repair,
            channel_id,
            days,
            use_zero_growth,
            use_estimation
        )

        return {
            "message": f"频道 {channel_id} 紧急修复任务已提交",
            "channel_id": channel_id,
            "channel_name": channel_info.get('channel_name', 'Unknown'),
            "emergency_config": {
                "days": days,
                "use_zero_growth": use_zero_growth,
                "use_estimation": use_estimation
            },
            "warning": "紧急修复会创建估算数据，请注意数据质量标记",
            "status": "submitted",
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"提交紧急修复任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"提交紧急修复任务失败: {str(e)}")

@router.post("/trigger/channel-update")
async def trigger_channel_update(background_tasks: BackgroundTasks):
    """手动触发频道信息更新"""
    try:
        logger.info("手动触发频道信息更新")
        
        # 在后台执行频道更新
        background_tasks.add_task(perform_channel_update)
        
        return {
            "message": "频道信息更新任务已提交",
            "status": "submitted",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"触发频道更新失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"触发频道更新失败: {str(e)}")

@router.post("/trigger/statistics")
async def trigger_statistics(background_tasks: BackgroundTasks, target_date: Optional[str] = None):
    """手动触发统计任务"""
    try:
        if target_date:
            target_date_obj = datetime.strptime(target_date, "%Y-%m-%d")
        else:
            target_date_obj = datetime.now() - timedelta(days=1)
        
        logger.info(f"手动触发统计任务，目标日期: {target_date_obj.date()}")
        
        # 在后台执行统计任务
        background_tasks.add_task(perform_statistics_update, target_date_obj)
        
        return {
            "message": f"统计任务已提交，目标日期: {target_date_obj.date()}",
            "target_date": target_date_obj.date().isoformat(),
            "status": "submitted",
            "timestamp": datetime.now().isoformat()
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"日期格式错误: {str(e)}")
    except Exception as e:
        logger.error(f"触发统计任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"触发统计任务失败: {str(e)}")

@router.get("/tasks/recent")
async def get_recent_tasks(hours: int = 24, limit: int = 50):
    """获取最近的任务执行情况"""
    try:
        recent_tasks = TaskDao.get_recent_tasks(hours=hours, limit=limit)
        
        # 统计任务执行情况
        task_stats = {}
        for task in recent_tasks:
            task_type = task.get('task_type', 'unknown')
            status = task.get('status', 'unknown')
            
            if task_type not in task_stats:
                task_stats[task_type] = {'total': 0, 'success': 0, 'failed': 0, 'pending': 0, 'running': 0}
            
            task_stats[task_type]['total'] += 1
            task_stats[task_type][status] = task_stats[task_type].get(status, 0) + 1
        
        return {
            "timestamp": datetime.now().isoformat(),
            "period_hours": hours,
            "total_tasks": len(recent_tasks),
            "task_statistics": task_stats,
            "recent_tasks": recent_tasks[:20]  # 只返回最近20条
        }
        
    except Exception as e:
        logger.error(f"获取最近任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取最近任务失败: {str(e)}")

# 辅助函数

def get_next_execution_time(hour: int, minute: int) -> str:
    """计算下次执行时间"""
    now = datetime.now()
    next_time = now.replace(hour=hour, minute=minute, second=0, microsecond=0)
    
    if now.hour > hour or (now.hour == hour and now.minute >= minute):
        next_time = next_time + timedelta(days=1)
    
    return next_time.isoformat()

async def perform_data_repair(days: int):
    """执行数据修复"""
    try:
        logger.info(f"开始执行数据修复，检查最近 {days} 天")
        
        integrity_result = ChannelStatsUpdater.check_snapshot_data_integrity(days=days)
        
        if 'error' in integrity_result:
            logger.error(f"数据修复失败: {integrity_result['error']}")
            return
        
        missing_statistics = integrity_result.get('missing_statistics', [])
        
        if missing_statistics:
            # 按日期分组并修复
            missing_by_date = {}
            for item in missing_statistics:
                date_str = item['missing_date']
                if date_str not in missing_by_date:
                    missing_by_date[date_str] = []
                missing_by_date[date_str].append(item)
            
            repaired_count = 0
            for date_str in missing_by_date:
                try:
                    target_date = datetime.strptime(date_str, '%Y-%m-%d')
                    success = ChannelStatsUpdater.update_all_channel_statistics(target_date)
                    if success:
                        repaired_count += len(missing_by_date[date_str])
                        logger.info(f"成功修复 {date_str} 的数据")
                except Exception as e:
                    logger.error(f"修复 {date_str} 数据失败: {str(e)}")
            
            logger.info(f"数据修复完成，修复了 {repaired_count} 条数据")
        else:
            logger.info("没有发现缺失的数据")
            
    except Exception as e:
        logger.error(f"数据修复过程异常: {str(e)}")

async def perform_channel_update():
    """执行频道信息更新"""
    try:
        logger.info("开始执行频道信息更新")
        success = update_channel_info_snapshot()
        logger.info(f"频道信息更新完成: {'成功' if success else '失败'}")
    except Exception as e:
        logger.error(f"频道信息更新异常: {str(e)}")

async def perform_statistics_update(target_date: datetime):
    """执行统计数据更新"""
    try:
        logger.info(f"开始执行统计数据更新，目标日期: {target_date.date()}")
        success = ChannelStatsUpdater.update_all_channel_statistics(target_date)
        logger.info(f"统计数据更新完成: {'成功' if success else '失败'}")
    except Exception as e:
        logger.error(f"统计数据更新异常: {str(e)}")

async def perform_channel_data_repair(
    channel_id: str,
    days: int,
    repair_snapshots: bool = True,
    repair_statistics: bool = True,
    force_repair: bool = False
):
    """执行指定频道的数据修复"""
    try:
        from src.dao.channel_dao import ChannelDao
        from src.services.channel_service import update_channel_info_snapshot

        logger.info(f"开始执行频道 {channel_id} 的数据修复，检查最近 {days} 天")

        # 获取频道信息
        channel_info = ChannelDao.get_channel_by_id(channel_id)
        if not channel_info:
            logger.error(f"频道 {channel_id} 不存在，无法执行修复")
            return

        channel_name = channel_info.get('channel_name', 'Unknown')
        logger.info(f"开始修复频道: {channel_name} ({channel_id})")

        # 检查频道数据完整性
        integrity_result = ChannelStatsUpdater.check_channel_data_integrity(channel_id, days)

        if 'error' in integrity_result:
            logger.error(f"频道 {channel_id} 数据完整性检查失败: {integrity_result['error']}")
            return

        missing_snapshots = integrity_result.get('missing_snapshots', [])
        missing_statistics = integrity_result.get('missing_statistics', [])

        repair_summary = {
            'snapshots_repaired': 0,
            'statistics_repaired': 0,
            'snapshots_failed': 0,
            'statistics_failed': 0
        }

        # 修复快照数据
        if repair_snapshots and missing_snapshots:
            logger.info(f"频道 {channel_id} 发现 {len(missing_snapshots)} 条缺失快照，开始修复")

            # 按日期分组缺失的快照
            missing_snapshot_dates = set(item['missing_date'] for item in missing_snapshots)

            for date_str in missing_snapshot_dates:
                try:
                    logger.info(f"尝试修复频道 {channel_id} 在 {date_str} 的快照数据")

                    if force_repair:
                        # 强制修复模式：尝试调用实时API获取快照数据
                        success = await attempt_snapshot_repair(channel_id, date_str)
                        if success:
                            repair_summary['snapshots_repaired'] += 1
                            logger.info(f"成功修复频道 {channel_id} 在 {date_str} 的快照数据")
                        else:
                            repair_summary['snapshots_failed'] += 1
                            logger.warning(f"强制修复频道 {channel_id} 在 {date_str} 的快照数据失败")
                    else:
                        # 普通模式：记录需要手动处理
                        logger.warning(f"频道 {channel_id} 在 {date_str} 的快照数据需要手动修复（需要API调用）")
                        logger.info(f"提示：可以使用 force_repair=true 参数尝试自动修复")
                        repair_summary['snapshots_failed'] += 1

                except Exception as e:
                    logger.error(f"修复频道 {channel_id} 在 {date_str} 的快照数据失败: {str(e)}")
                    repair_summary['snapshots_failed'] += 1

        # 修复统计数据
        if repair_statistics and missing_statistics:
            logger.info(f"频道 {channel_id} 发现 {len(missing_statistics)} 条缺失统计，开始修复")

            # 按日期分组缺失的统计数据
            missing_stats_by_date = {}
            for item in missing_statistics:
                date_str = item['missing_date']
                if date_str not in missing_stats_by_date:
                    missing_stats_by_date[date_str] = []
                missing_stats_by_date[date_str].append(item)

            # 尝试修复每个缺失的日期
            for date_str in missing_stats_by_date:
                try:
                    target_date = datetime.strptime(date_str, '%Y-%m-%d')
                    logger.info(f"尝试修复频道 {channel_id} 在 {date_str} 的统计数据")

                    # 使用增强的统计计算逻辑
                    if force_repair:
                        # 强制修复模式：使用更激进的修复策略
                        success = await attempt_force_statistics_repair(channel_id, target_date)
                    else:
                        # 普通模式：使用标准的增强统计计算逻辑
                        success = ChannelStatsUpdater.process_daily_statistics(channel_id, target_date)

                    if success:
                        repair_summary['statistics_repaired'] += 1
                        logger.info(f"成功修复频道 {channel_id} 在 {date_str} 的统计数据")
                    else:
                        repair_summary['statistics_failed'] += 1
                        if not force_repair:
                            logger.warning(f"修复频道 {channel_id} 在 {date_str} 的统计数据失败")
                            logger.info(f"提示：可以使用 force_repair=true 参数尝试强制修复")
                        else:
                            logger.warning(f"强制修复频道 {channel_id} 在 {date_str} 的统计数据也失败了")

                except Exception as e:
                    logger.error(f"修复频道 {channel_id} 在 {date_str} 的统计数据时出错: {str(e)}")
                    repair_summary['statistics_failed'] += 1

        # 输出修复总结
        total_repaired = repair_summary['statistics_repaired']
        total_failed = repair_summary['snapshots_failed'] + repair_summary['statistics_failed']

        if total_repaired > 0:
            logger.info(f"频道 {channel_name} ({channel_id}) 数据修复完成: "
                       f"成功修复 {total_repaired} 条统计数据, "
                       f"失败 {total_failed} 条")
        else:
            if missing_snapshots or missing_statistics:
                logger.warning(f"频道 {channel_name} ({channel_id}) 数据修复未成功修复任何数据")
            else:
                logger.info(f"频道 {channel_name} ({channel_id}) 数据完整，无需修复")

    except Exception as e:
        logger.error(f"频道 {channel_id} 数据修复过程异常: {str(e)}")

async def attempt_snapshot_repair(channel_id: str, date_str: str) -> bool:
    """尝试修复快照数据 - 强制修复模式"""
    try:
        from src.services.channel_service import update_channel_info_snapshot

        logger.info(f"强制修复模式：尝试为频道 {channel_id} 获取 {date_str} 的快照数据")

        # 方案1: 尝试调用当前的快照更新服务
        # 注意：这会获取当前时间的数据，不是历史数据
        success = update_channel_info_snapshot()
        if success:
            logger.info(f"成功调用快照更新服务，但获取的是当前数据而非 {date_str} 的历史数据")
            return True

        # 方案2: 如果有历史数据API，可以在这里调用
        # TODO: 实现历史快照数据获取逻辑

        return False

    except Exception as e:
        logger.error(f"强制修复快照数据失败: {str(e)}")
        return False

async def attempt_force_statistics_repair(channel_id: str, target_date: datetime) -> bool:
    """尝试强制修复统计数据"""
    try:
        logger.info(f"强制修复模式：尝试为频道 {channel_id} 修复 {target_date.date()} 的统计数据")

        # 方案1: 使用更大范围的历史数据进行估算
        success = ChannelStatsUpdater.force_calculate_statistics(channel_id, target_date)
        if success:
            return True

        # 方案2: 使用零增长假设创建统计记录
        success = create_zero_growth_statistics(channel_id, target_date)
        if success:
            logger.warning(f"使用零增长假设为频道 {channel_id} 创建了 {target_date.date()} 的统计数据")
            return True

        return False

    except Exception as e:
        logger.error(f"强制修复统计数据失败: {str(e)}")
        return False

def create_zero_growth_statistics(channel_id: str, target_date: datetime) -> bool:
    """创建零增长假设的统计数据"""
    try:
        # 查找最近可用的统计数据作为基准
        recent_stats = ChannelStatsUpdater.get_recent_statistics(channel_id, days=30)

        if not recent_stats:
            logger.warning(f"频道 {channel_id} 没有找到最近30天的统计数据，无法创建零增长统计")
            return False

        # 使用最近的统计数据作为基准，假设零增长
        base_stats = recent_stats[0]

        yesterday_start = (target_date - timedelta(days=1)).replace(hour=0, minute=0, second=0, microsecond=0)
        today_end = target_date.replace(hour=23, minute=59, second=59, microsecond=999999)

        # 创建零增长的统计记录
        success = ChannelStatsUpdater.save_period_statistics(
            channel_id=channel_id,
            period_type="day",
            period_start=yesterday_start,
            period_end=today_end,
            start_subscribers=base_stats.get('end_subscribers', 0),
            end_subscribers=base_stats.get('end_subscribers', 0),  # 零增长
            start_views=base_stats.get('end_views', 0),
            end_views=base_stats.get('end_views', 0),  # 零增长
            start_revenue=0,
            end_revenue=0,
            year=target_date.year,
            month=target_date.month,
            day=target_date.day
        )

        if success:
            logger.info(f"成功为频道 {channel_id} 创建零增长统计数据 ({target_date.date()})")

        return success

    except Exception as e:
        logger.error(f"创建零增长统计数据失败: {str(e)}")
        return False

async def perform_emergency_repair(
    channel_id: str,
    days: int,
    use_zero_growth: bool = True,
    use_estimation: bool = True
):
    """执行紧急修复"""
    try:
        from src.dao.channel_dao import ChannelDao

        logger.info(f"开始执行频道 {channel_id} 的紧急修复，修复最近 {days} 天")

        # 获取频道信息
        channel_info = ChannelDao.get_channel_by_id(channel_id)
        if not channel_info:
            logger.error(f"频道 {channel_id} 不存在，无法执行紧急修复")
            return

        channel_name = channel_info.get('channel_name', 'Unknown')
        logger.info(f"开始紧急修复频道: {channel_name} ({channel_id})")

        # 检查数据完整性
        integrity_result = ChannelStatsUpdater.check_channel_data_integrity(channel_id, days)

        if 'error' in integrity_result:
            logger.error(f"频道 {channel_id} 数据完整性检查失败: {integrity_result['error']}")
            return

        missing_statistics = integrity_result.get('missing_statistics', [])

        if not missing_statistics:
            logger.info(f"频道 {channel_id} 数据完整，无需紧急修复")
            return

        logger.info(f"频道 {channel_id} 发现 {len(missing_statistics)} 条缺失统计数据，开始紧急修复")

        repair_summary = {
            'zero_growth_created': 0,
            'estimation_created': 0,
            'failed': 0
        }

        # 按日期分组缺失的统计数据
        missing_stats_by_date = {}
        for item in missing_statistics:
            date_str = item['missing_date']
            if date_str not in missing_stats_by_date:
                missing_stats_by_date[date_str] = []
            missing_stats_by_date[date_str].append(item)

        # 尝试修复每个缺失的日期
        for date_str in missing_stats_by_date:
            try:
                target_date = datetime.strptime(date_str, '%Y-%m-%d')
                logger.info(f"紧急修复频道 {channel_id} 在 {date_str} 的统计数据")

                success = False

                # 策略1: 使用数据估算
                if use_estimation and not success:
                    success = ChannelStatsUpdater.force_calculate_statistics(channel_id, target_date)
                    if success:
                        repair_summary['estimation_created'] += 1
                        logger.info(f"使用数据估算成功修复频道 {channel_id} 在 {date_str} 的数据")

                # 策略2: 使用零增长假设
                if use_zero_growth and not success:
                    success = create_zero_growth_statistics(channel_id, target_date)
                    if success:
                        repair_summary['zero_growth_created'] += 1
                        logger.info(f"使用零增长假设成功修复频道 {channel_id} 在 {date_str} 的数据")

                if not success:
                    repair_summary['failed'] += 1
                    logger.error(f"紧急修复频道 {channel_id} 在 {date_str} 的数据失败")

            except Exception as e:
                logger.error(f"紧急修复频道 {channel_id} 在 {date_str} 的数据时出错: {str(e)}")
                repair_summary['failed'] += 1

        # 输出修复总结
        total_repaired = repair_summary['estimation_created'] + repair_summary['zero_growth_created']

        if total_repaired > 0:
            logger.info(f"频道 {channel_name} ({channel_id}) 紧急修复完成: "
                       f"估算数据 {repair_summary['estimation_created']} 条, "
                       f"零增长数据 {repair_summary['zero_growth_created']} 条, "
                       f"失败 {repair_summary['failed']} 条")
        else:
            logger.warning(f"频道 {channel_name} ({channel_id}) 紧急修复未成功修复任何数据")

    except Exception as e:
        logger.error(f"频道 {channel_id} 紧急修复过程异常: {str(e)}")
