# 导入所需模块
import time
import os
import sys
import traceback
import asyncio
from pathlib import Path
from dotenv import load_dotenv

from fastapi import FastAPI, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

# 添加项目根目录到 Python 路径
project_root = str(Path(__file__).parent.parent)
if project_root not in sys.path:
    sys.path.append(project_root)

# 加载环境变量
load_dotenv()

# 导入日志配置函数并设置日志
from src.utils.logger import setup_logging_from_config, get_logger
setup_logging_from_config()

# 导入其他项目模块
from src.database.db_pool import DatabasePool
from src.dao.channel_dao import ChannelDao
from src.dao.task_dao import TaskDao
from src.dao.raw_fetch_dao import RawFetchDao
from src.dao.youtube_dao import YouTubeDao
from src.utils.channel_stats_updater import ChannelStatsUpdater
from src.routers import router as api_router
from src.models.schemas import ErrorResponse
from src.jobs import start_all

# 设置日志
logger = get_logger('app')

# 从环境变量获取YouTube API密钥
YOUTUBE_API_KEY = os.getenv("YOUTUBE_API_KEY", "")

if not YOUTUBE_API_KEY:
    logger.error("未配置YouTube API密钥，请在.env文件中设置YOUTUBE_API_KEY")
    sys.exit(1)

# 创建FastAPI应用
app = FastAPI(
    title="YouTube Data API",
    description="提供YouTube频道和视频数据采集、处理和查询的API",
    version="2.0.0",
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源，生产环境建议配置具体的来源列表
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有方法
    allow_headers=["*"],  # 允许所有头部
)

# 添加日志中间件
@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = time.time()
    
    # 记录请求信息
    logger.info(f"请求开始: {request.method} {request.url.path}")
    
    # 处理请求
    response = await call_next(request)
    
    # 计算处理时间
    process_time = (time.time() - start_time) * 1000
    logger.info(f"请求完成: {request.method} {request.url.path} - 状态: {response.status_code} - 耗时: {process_time:.2f}ms")
    
    return response

# 添加全局异常处理器
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    logger.error(f"全局异常: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            code=500,
            message="服务器内部错误",
            details=str(exc)
        ).dict(),
    )

# 添加API路由
app.include_router(api_router)

@app.get("/", include_in_schema=False)
async def root():
    return {"message": "欢迎使用YouTube数据API，访问 /docs 查看文档"}

# 应用启动事件
@app.on_event("startup")
async def startup_event():
    logger.info("应用启动中...")
    try:
        # 初始化数据库连接池
        db_pool = DatabasePool() # 直接创建实例初始化连接池
        logger.info("数据库连接池初始化成功")
        
        db_pool.init_clients()
        # 确保统计数据表结构已创建
        ChannelStatsUpdater.ensure_database_structure()
        
        # 启动后台任务
        asyncio.create_task(start_all())
        
        logger.info("应用初始化成功")
    except Exception as e:
        logger.error(f"应用初始化失败: {str(e)}")
        logger.error("".join(traceback.format_exception(type(e), e, e.__traceback__)))

# 应用关闭事件
@app.on_event("shutdown")
async def shutdown_event():
    logger.info("应用正在关闭...")
    try:
        # 这里不需要显式关闭数据库连接池，Python的垃圾回收会处理
        logger.info("资源清理完成")
    except Exception as e:
        logger.error(f"资源清理失败: {str(e)}")
        logger.error("".join(traceback.format_exception(type(e), e, e.__traceback__))) 

# 如果直接运行此文件
if __name__ == "__main__":
    import uvicorn
    uvicorn.run("src.app:app", host="0.0.0.0", port=8100, reload=True) 