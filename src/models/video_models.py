from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List

class VideoDownloadRequest(BaseModel):
    video_url: str = Field(..., description="YouTube视频URL或ID")
    format: str = Field("best", description="视频格式，例如：best, bestvideo+bestaudio, 360p, 720p")

class VideoClipRequest(BaseModel):
    video_url: str = Field(..., description="YouTube视频URL或ID")
    start_time: float = Field(..., description="开始时间（秒）")
    end_time: float = Field(..., description="结束时间（秒）")
    format: str = Field("best[ext=mp4]", description="视频格式")

class AudioDownloadRequest(BaseModel):
    video_url: str = Field(..., description="YouTube视频URL或ID")
    format: str = Field("bestaudio[ext=m4a]", description="音频格式")

class ChannelVideoCategoriesRequest(BaseModel):
    channel_id: str = Field(..., description="YouTube频道ID")
    max_results: int = Field(5, description="最大获取视频数量，默认5")
    region_code: str = Field("US", description="地区代码，默认US")

class VideoCategory(BaseModel):
    video_id: str = Field(..., description="视频ID")
    category_id: str = Field(..., description="分类ID")
    category_title: str = Field(..., description="分类名称")

class VideoCategoriesResponse(BaseModel):
    channel_id: str = Field(..., description="频道ID")
    videos: List[VideoCategory] = Field(..., description="视频分类列表")
    total_count: int = Field(..., description="总视频数量")